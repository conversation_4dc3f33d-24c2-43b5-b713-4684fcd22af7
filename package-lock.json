{"name": "user-app-cdk", "version": "0.1.0", "lockfileVersion": 3, "requires": true, "packages": {"": {"name": "user-app-cdk", "version": "0.1.0", "dependencies": {"@aws-cdk/aws-dynamodb": "^1.204.0", "@aws-cdk/aws-ec2": "^1.204.0", "@aws-cdk/aws-ecs": "^1.204.0", "@aws-cdk/aws-iam": "^1.204.0", "@aws-cdk/aws-logs": "^1.204.0", "@aws-cdk/aws-servicediscovery": "^1.204.0", "aws-cdk-lib": "2.196.0", "constructs": "^10.0.0"}, "bin": {"user-app-cdk": "bin/user-app-cdk.js"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.1016.1", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}}, "node_modules/@ampproject/remapping": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/@ampproject/remapping/-/remapping-2.3.0.tgz", "integrity": "sha512-30iZtAPgz+LTIYoeivqYo853f02jBYSd5uGnGpkFV0M3xOt9aN73erkgYAmZU43x4VfqcnLxW9Kpg3R5LC4YYw==", "dev": true, "dependencies": {"@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.24"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@aws-cdk/asset-awscli-v1": {"version": "2.2.236", "resolved": "https://registry.npmjs.org/@aws-cdk/asset-awscli-v1/-/asset-awscli-v1-2.2.236.tgz", "integrity": "sha512-BjqQVGYsVuS4VXdrezDapSd6P7soEdWJoXl1S8X7l0uLtVX9WvpmCylZKOJDrJblK5MNe1Vq9wUI91LBzzOi8A=="}, "node_modules/@aws-cdk/asset-node-proxy-agent-v6": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/@aws-cdk/asset-node-proxy-agent-v6/-/asset-node-proxy-agent-v6-2.1.0.tgz", "integrity": "sha512-7bY3J8GCVxLupn/kNmpPc5VJz8grx+4RKfnnJiO1LG+uxkZfANZG3RMHhE+qQxxwkyQ9/MfPtTpf748UhR425A=="}, "node_modules/@aws-cdk/assets": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/assets/-/assets-1.204.0.tgz", "integrity": "sha512-rY9YHZ3gUWr+dLwTwSUWYbIfk/AXy4JZRkhLbunrtzjQhH+QMm/2IWIebfBGu+A5AlVRaFbRLonReuGP5WZoUQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/assets/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-acmpca": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-acmpca/-/aws-acmpca-1.204.0.tgz", "integrity": "sha512-2zyuQZwynwkz2qiuFDp088tglWXKX3q7saWRDqeuq2n2HE6PGuQRjd4zjl9nDGUVxQYtzzXyEuPyaEta8fg9lQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-acmpca/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-apigateway": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-apigateway/-/aws-apigateway-1.204.0.tgz", "integrity": "sha512-uVK309Ltdq/Q0w0tREtIIJYKAeevKwU/oqVMy+PKis1+bSEesN64hIPAd+qqBuChrzCdhEDtY9oiKGOTvM0Alg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-cognito": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-stepfunctions": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-cognito": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-stepfunctions": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-apigateway/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-applicationautoscaling": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-applicationautoscaling/-/aws-applicationautoscaling-1.204.0.tgz", "integrity": "sha512-sEe2NODKUowJx2guM2SPfs/20gGdBq1C09M32b8c1im7K+PqQkHkE156nyz5Ml0hpsNeCZlRS17oKZ042aZevQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-autoscaling-common": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-autoscaling-common": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-applicationautoscaling/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-autoscaling": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-autoscaling/-/aws-autoscaling-1.204.0.tgz", "integrity": "sha512-LVQK84GR6U0RXfPbxZauWDCfcgBgH4bofOHx1sfr6yz7FTSYYkYM2tMJl81SmVc6ueSJzfhFtIcWhSpMBH9yig==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-autoscaling-common": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-autoscaling-common": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-autoscaling-common": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-autoscaling-common/-/aws-autoscaling-common-1.204.0.tgz", "integrity": "sha512-P+PwbTaj28Eg9+/U9ZTXTh1gA7L9Z45GL+9xcEZvEqAkJt9MNgzZICavVZu1sMD74foK1r1ZOBXTsqV6wEiltQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-autoscaling-common/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-autoscaling-hooktargets": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-autoscaling-hooktargets/-/aws-autoscaling-hooktargets-1.204.0.tgz", "integrity": "sha512-FiS2SqrBtNg4Cpf1rmJcxQwKJE6pGgzyKLnMWzHITDpYur3D3rKNuWSq2PW/60mjIfkBudcMFE1GIdgBxZNeUQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-autoscaling": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sns-subscriptions": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-autoscaling": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sns-subscriptions": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-autoscaling-hooktargets/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-autoscaling/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-certificatemanager": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-certificatemanager/-/aws-certificatemanager-1.204.0.tgz", "integrity": "sha512-ZLykfAOb5Zbg/MFtzA+eHhMAK1xL32+oHKSK6tAYrgvv2aS42wJE4zSBV6jGCjnCkhcliUd5pwnACEl3ib0KLw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-acmpca": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-acmpca": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-certificatemanager/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-cloudformation": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-cloudformation/-/aws-cloudformation-1.204.0.tgz", "integrity": "sha512-9PkZa9mKLneB0My8wJC7lLZmPJsnOxNYy57ZZlRCQhK0eO6Jc9eVqrI29537W+3ireaEjCLEitkb8NO1FN/kQA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-cloudformation/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-cloudfront": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-cloudfront/-/aws-cloudfront-1.204.0.tgz", "integrity": "sha512-bgqGsImVjFQJihDvLg0hWRtmq2b+HVj94Fngz/zo4PsB5kTt1QZvHOk2HNBkozNhDK8LXysHtdKvmzpaK29TJQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-cloudfront/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-cloudwatch": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-cloudwatch/-/aws-cloudwatch-1.204.0.tgz", "integrity": "sha512-ADT2D+4FtB9Zcy/TlF2tswQmjmrPVgORYTkznQQ2SniCODHWzz558+G1RV+IVvWRdH7nYQtV0UEuGZKpffWh2w==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-cloudwatch/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-codeguruprofiler": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-codeguruprofiler/-/aws-codeguruprofiler-1.204.0.tgz", "integrity": "sha512-IrgY4SmVf9p5POfHm8BsPzaAO5lQTG7nhb5qN5AzS6zKCTuEjjTNHjx1TOfPV12mMIDAIVsK91mjDlAR88Mjbg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-codeguruprofiler/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-codestarnotifications": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-codestarnotifications/-/aws-codestarnotifications-1.204.0.tgz", "integrity": "sha512-t//hSpC5/uVW2321YlbGabNVzhWayvqz+xSnagADGcT9qiq3KQR/uUlrgpHv1/eHRMk7EMrY9prlXeZpfzZ+cw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-codestarnotifications/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-cognito": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-cognito/-/aws-cognito-1.204.0.tgz", "integrity": "sha512-7QIbExW9dn1fktpDOh2nMHmor2S3uuHtIX5y33lc9OKg3xUuYw4AZ67MKapunN7QUBlffTlNzoUqlHoNSab+Zg==", "bundleDependencies": ["punycode"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69", "punycode": "^2.3.0"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-cognito/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-cognito/node_modules/punycode": {"version": "2.3.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/@aws-cdk/aws-dynamodb": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-dynamodb/-/aws-dynamodb-1.204.0.tgz", "integrity": "sha512-mJhbrmLiRa2etpzH2Uyz5429+YXIQeZqg0uXESEDLf5xCoypmwXn3zdxcBlz2sIbhTZcWta8s+dqIIor+GcMJQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kinesis": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kinesis": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-dynamodb/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-ec2": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-ec2/-/aws-ec2-1.204.0.tgz", "integrity": "sha512-SoqZEgzdfPW0aa+FQ0CjzbDG+X+sDu6/BnLL2O10lxpa+9Dc1iyArAqNKFJG5KXGJe9ibvQXyNQqEjeGRFc22Q==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-ec2/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-ec2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-ec2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-ec2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-ec2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-ec2/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-ecr": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-ecr/-/aws-ecr-1.204.0.tgz", "integrity": "sha512-oCts9e+ackWoFHeyn/3oKm3X1lSizleWNNXHp5WGM38lpNVrtCLMKSShu5iXJBhqRH2Mz1AcA4fDMWhe8DvJFA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-ecr-assets": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-ecr-assets/-/aws-ecr-assets-1.204.0.tgz", "integrity": "sha512-2GHD3pZdDoPxq3HhD4czANuI7TMoxpjszbzsQAc2wbdMX1j+K4vIL+PBpj3altfscPqcvy1v70lBjbG5rcBIkQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/assets": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/assets": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-ecr-assets/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-ecr/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-ecs": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-ecs/-/aws-ecs-1.204.0.tgz", "integrity": "sha512-YhGkLMyjK2e+czTLBlMU/B2kj2DXnIT3+uoLqJ5Go0XeRbeXO2d/D0WdYSBaXWr7oBGpg37oPjpPLbswId7ZTw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-autoscaling": "1.204.0", "@aws-cdk/aws-autoscaling-hooktargets": "1.204.0", "@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-ecr-assets": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-route53-targets": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-secretsmanager": "1.204.0", "@aws-cdk/aws-servicediscovery": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-autoscaling": "1.204.0", "@aws-cdk/aws-autoscaling-hooktargets": "1.204.0", "@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-ecr-assets": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-route53-targets": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-secretsmanager": "1.204.0", "@aws-cdk/aws-servicediscovery": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/aws-ssm": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-ecs/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-efs": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-efs/-/aws-efs-1.204.0.tgz", "integrity": "sha512-FB6nHgCuzYF5K9ywqYPEPjL2G1ATLIR9dJp1p4ydcEUuXDb4KSEVN4Bgx+q1e7EkWGIq+9glr+ckheEcTvETgw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-efs/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-efs/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-efs/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-efs/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-efs/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-efs/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-elasticloadbalancing": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-elasticloadbalancing/-/aws-elasticloadbalancing-1.204.0.tgz", "integrity": "sha512-DhC6hrWqi6EaRLcDBAVrE+/iGa+WUUtLla4oqkLVj/C76VcM1CkCyH/9IX8O1CK0NswtC6AxOF3+z4uroTUWDg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-elasticloadbalancing/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-elasticloadbalancingv2/-/aws-elasticloadbalancingv2-1.204.0.tgz", "integrity": "sha512-/43kzUTU3w9jimPuD5QZxoBN74+9QnOdhAcqIMVCFLPMkVLAxx3vg5g5MWWG+3j6rUoSecrtrP1AP7thZuo5wA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-certificatemanager": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-elasticloadbalancingv2/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-events": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-events/-/aws-events-1.204.0.tgz", "integrity": "sha512-KnfUmtv+4RhydD9+5CHFxNJxtgn7+Xftwfwg1G7qV/tWYPFHcNIvhlSOgwDrQPa+pTo1MmkiUN0lAR0G8B/cbw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-events/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-globalaccelerator": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-globalaccelerator/-/aws-globalaccelerator-1.204.0.tgz", "integrity": "sha512-B92vKAncsGV2wxcbHeg230EZibSuzianVu3z0CDVS2EQnQ1A68Ff75JWPFGGnfiJrSnKN/pvzrDKV5Z45bhm4Q==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-globalaccelerator/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-iam": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-iam/-/aws-iam-1.204.0.tgz", "integrity": "sha512-Fh2egW3v/uDdw3m4jvcupS7COL/+sJl2NHjz9l298ddyMxqDwJD2NQwE8mvgPLK4rDtAtDnE0c8RS6d+NWiC+w==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-iam/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-kinesis": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-kinesis/-/aws-kinesis-1.204.0.tgz", "integrity": "sha512-CZm0Qk0PoagLZ81XadxgKFM0/20fhFuYRnT98oqdvsrqvYJddXaKt/peCI5v/PbRtptGkpn2FoHjkwxKWoJNJA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-kinesis/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-kms": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-kms/-/aws-kms-1.204.0.tgz", "integrity": "sha512-iryZQ428L1VUVQ0zE96XTWWX7ANVtDrb6x+ZXXLTVUEPgjEd/W6zlcp++Qi0A3a9HLNd4PbEhK9rs0UKNTylzw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-kms/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-kms/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-kms/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-kms/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-kms/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-kms/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-lambda": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-lambda/-/aws-lambda-1.204.0.tgz", "integrity": "sha512-r0XXovrLAx8Q8Fz915SwzyQM/KLhEB6YCp3CsWliFGSOHEjRP8yX8UZdEJqe5kYD7Th9JAhUVzKgyv20P7g5Tg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-codeguruprofiler": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-ecr-assets": "1.204.0", "@aws-cdk/aws-efs": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-signer": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-applicationautoscaling": "1.204.0", "@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-codeguruprofiler": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-ecr": "1.204.0", "@aws-cdk/aws-ecr-assets": "1.204.0", "@aws-cdk/aws-efs": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/aws-signer": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-lambda/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-logs": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-logs/-/aws-logs-1.204.0.tgz", "integrity": "sha512-PuHsDSkX6JFBgldxViGw91eFLageJ2cX89/RyLbWaJJUV4tlUKXSmmkVgOaBmvil0QKuGqbOzLXcXCoIK9Sg3A==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-s3-assets": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-logs/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-route53": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-route53/-/aws-route53-1.204.0.tgz", "integrity": "sha512-wQpGUXqc2y7yJFTipfuVxWy/VGeshyGlfGl4evusQK9Md0DMpVmG8kRgazLk1myqUSNSfi643UwvDJqNbYmdnA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/custom-resources": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-route53-targets": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-route53-targets/-/aws-route53-targets-1.204.0.tgz", "integrity": "sha512-JyILJz/HGRMilpFxrDk/VXv+TN24DoG5Gfdfh8SJoJpptokowN8blaQ2ibf6N0JnFqWSBrs7gMMWB2dR/sXoTQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-apigateway": "1.204.0", "@aws-cdk/aws-cloudfront": "1.204.0", "@aws-cdk/aws-cognito": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-globalaccelerator": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-apigateway": "1.204.0", "@aws-cdk/aws-cloudfront": "1.204.0", "@aws-cdk/aws-cognito": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancing": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-globalaccelerator": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-route53-targets/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-route53/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-route53/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-route53/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-route53/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-route53/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-route53/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-s3": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-s3/-/aws-s3-1.204.0.tgz", "integrity": "sha512-jsQ4n1L4MdPYDirBoOYgg7yzSk1TaFYo4dnwDlKiLJ5LcHG3Nai1cHb9XQbCy/9KKqbWsbd3WlkH+vcWEl8EUA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-s3-assets": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-s3-assets/-/aws-s3-assets-1.204.0.tgz", "integrity": "sha512-3MQbVZ95wW29Bl63tqu0Bz0td3osLyGg352l5G7Ztf3nK35FpuQlgxO4kcu74+s2sRwdd/R4KFV6eWhhPk+J7g==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/assets": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/assets": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-s3-assets/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-s3/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-sam": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-sam/-/aws-sam-1.204.0.tgz", "integrity": "sha512-8M3e1ZT6/nO6Yxtr7YtcrTF5YG6kjw8PcnOjda0SfUFo8Xya7oi1OZvDRIfAeOexsIgqjKSV8s7brDrWM5DIpg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-sam/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-secretsmanager": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-secretsmanager/-/aws-secretsmanager-1.204.0.tgz", "integrity": "sha512-ykpjYmP6qVOFbHtkaQBu3Xk7xp2UTR0ouzk7pb+zrEHKGmRvzGq+8J0IU+qXBJgQIVwFAPf2IgOSTzj6FJPdyA==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sam": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sam": "1.204.0", "@aws-cdk/core": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-secretsmanager/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-servicediscovery": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-servicediscovery/-/aws-servicediscovery-1.204.0.tgz", "integrity": "sha512-K1ckza6oAj3DntEAYmolm2JafkxJ0ekWb+DCl9hkm9l+546j28Qpb4cm8VkgGteNBN4JYACxrIuIxVC2zBLsCg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-elasticloadbalancingv2": "1.204.0", "@aws-cdk/aws-route53": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-servicediscovery/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-signer": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-signer/-/aws-signer-1.204.0.tgz", "integrity": "sha512-AI26FhWF3+f/vDh3mleQa2CXv2/CmSerXgyk4XHMVVTTCjnlYGGmHmGlzYhqOSw6ALpQNdOSw8GVxU/ySpQCaw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-signer/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-sns": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-sns/-/aws-sns-1.204.0.tgz", "integrity": "sha512-KoWxqKT/dTjt9Pk0a3kJLcd6xZHvrwbZDC0mrLtxdRNhQoHmnURAHW2UqX/lefrCU1GcUFf4L58N9ehBTunAFQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-codestarnotifications": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-codestarnotifications": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-sns-subscriptions": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-sns-subscriptions/-/aws-sns-subscriptions-1.204.0.tgz", "integrity": "sha512-yi78Kp0fV2nL7LnxL9ot8wbhGVYsL/ZeIzi6m2+iRZCgW1V+nO/a/eXdk5mMpBGLCqOtWcG59sIpSjqHvgpdaQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/aws-sqs": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-sns-subscriptions/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-sns/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-sqs": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-sqs/-/aws-sqs-1.204.0.tgz", "integrity": "sha512-dVzuGMh6d5/X9P9jel1w2Wgdy5MuSE35+eBSFxN+S7oJRoVSARpyKMNYAPMCW+2OJCDw7fIqO1rWbsZBT1Gq8g==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-sqs/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-ssm": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-ssm/-/aws-ssm-1.204.0.tgz", "integrity": "sha512-yYx7HZ8cWNXDAmX/99WkB477QhLoV2rcB8orei8aj7nRkNq5TMjeox0IJaZVgU+edNEDOi1fVX3flh0SAMiUrg==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-kms": "1.204.0", "@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-ssm/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/aws-ssm/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/aws-ssm/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-ssm/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/aws-ssm/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/aws-ssm/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/aws-stepfunctions": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/aws-stepfunctions/-/aws-stepfunctions-1.204.0.tgz", "integrity": "sha512-S8yuB5GtUajOxUcoMw82HQ+ei1U9uofwENEnEtYTeyqgjpd0FG4XHYoHvBdmgVvEKwpH/XiOePfEHeB8nTXufw==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudwatch": "1.204.0", "@aws-cdk/aws-events": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-s3": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/aws-stepfunctions/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/cloud-assembly-schema": {"version": "41.2.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-41.2.0.tgz", "integrity": "sha512-JaulVS6z9y5+u4jNmoWbHZRs9uGOnmn/ktXygNWKNu1k6lF3ad4so3s18eRu15XCbUIomxN9WPYT6Ehh7hzONw==", "bundleDependencies": ["jsonschema", "semver"], "dependencies": {"jsonschema": "~1.4.1", "semver": "^7.7.1"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.7.1", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/core": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/core/-/core-1.204.0.tgz", "integrity": "sha512-yO/flJ9ihpzRhLTEqlbdbuPGtyyghHiiQPkUTLslwUM5vThVTbpgvW4UQHSGqytyst4MYXrN2jQn2RkwIRU57g==", "bundleDependencies": ["fs-extra", "minimatch", "@balena/dockerignore", "ignore"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "@balena/dockerignore": "^1.0.2", "constructs": "^3.3.69", "fs-extra": "^9.1.0", "ignore": "^5.2.4", "minimatch": "^3.1.2"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/cloud-assembly-schema": "1.204.0", "@aws-cdk/cx-api": "1.204.0", "@aws-cdk/region-info": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/core/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/core/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/core/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/core/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/core/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/core/node_modules/@balena/dockerignore": {"version": "1.0.2", "inBundle": true, "license": "Apache-2.0"}, "node_modules/@aws-cdk/core/node_modules/at-least-node": {"version": "1.0.0", "inBundle": true, "license": "ISC", "engines": {"node": ">= 4.0.0"}}, "node_modules/@aws-cdk/core/node_modules/balanced-match": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/@aws-cdk/core/node_modules/brace-expansion": {"version": "1.1.11", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/@aws-cdk/core/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/@aws-cdk/core/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/core/node_modules/fs-extra": {"version": "9.1.0", "inBundle": true, "license": "MIT", "dependencies": {"at-least-node": "^1.0.0", "graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/core/node_modules/graceful-fs": {"version": "4.2.10", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/core/node_modules/ignore": {"version": "5.2.4", "inBundle": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/@aws-cdk/core/node_modules/jsonfile": {"version": "6.1.0", "inBundle": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/@aws-cdk/core/node_modules/minimatch": {"version": "3.1.2", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/@aws-cdk/core/node_modules/universalify": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/@aws-cdk/custom-resources": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/custom-resources/-/custom-resources-1.204.0.tgz", "integrity": "sha512-0w3oi7LnAtMZpf7uUBDH6aT2Oo1EBQrqD+VTvPZDX8PJFAox8ol7buZ9sSTpIXgv9j/GK9yaPTIHt4m8ok9kVQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/aws-cloudformation": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/aws-cloudformation": "1.204.0", "@aws-cdk/aws-ec2": "1.204.0", "@aws-cdk/aws-iam": "1.204.0", "@aws-cdk/aws-lambda": "1.204.0", "@aws-cdk/aws-logs": "1.204.0", "@aws-cdk/aws-sns": "1.204.0", "@aws-cdk/core": "1.204.0", "constructs": "^3.3.69"}}, "node_modules/@aws-cdk/custom-resources/node_modules/constructs": {"version": "3.4.344", "resolved": "https://registry.npmjs.org/constructs/-/constructs-3.4.344.tgz", "integrity": "sha512-Qq3upn44oGdvgasHUKWVFsrynyYrtVRd9fd8ko9cJOrFzx9eCm3iI4bhBryQqaISdausbTYUOXmoEe/YSJ16Nw==", "engines": {"node": ">= 16.14.0"}}, "node_modules/@aws-cdk/cx-api": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cx-api/-/cx-api-1.204.0.tgz", "integrity": "sha512-Juh/jL1kFPD5JcI9Uu6X0mM2L6hBCN5grdjSS40F8dThbH25VPzFBejaKjiy5nP1UZB83X+HW3utYOEi97DqxA==", "bundleDependencies": ["semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"@aws-cdk/cloud-assembly-schema": "1.204.0", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"@aws-cdk/cloud-assembly-schema": "1.204.0"}}, "node_modules/@aws-cdk/cx-api/node_modules/@aws-cdk/cloud-assembly-schema": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/cloud-assembly-schema/-/cloud-assembly-schema-1.204.0.tgz", "integrity": "sha512-DMNSR4DNKMNNfhOq1UizwZvesOKdhk3R3gRigrvWBHIkHMQg+W6aZFl7WZLKSBkChAXhIsH///psjhDQ20gl1w==", "bundleDependencies": ["jsonschema", "semver"], "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "dependencies": {"jsonschema": "^1.4.1", "semver": "^7.3.8"}, "engines": {"node": ">= 14.15.0"}}, "node_modules/@aws-cdk/cx-api/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/jsonschema": {"version": "1.4.1", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/@aws-cdk/cx-api/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/cx-api/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/cx-api/node_modules/@aws-cdk/cloud-assembly-schema/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/cx-api/node_modules/lru-cache": {"version": "6.0.0", "inBundle": true, "license": "ISC", "dependencies": {"yallist": "^4.0.0"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/cx-api/node_modules/semver": {"version": "7.3.8", "inBundle": true, "license": "ISC", "dependencies": {"lru-cache": "^6.0.0"}, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/@aws-cdk/cx-api/node_modules/yallist": {"version": "4.0.0", "inBundle": true, "license": "ISC"}, "node_modules/@aws-cdk/region-info": {"version": "1.204.0", "resolved": "https://registry.npmjs.org/@aws-cdk/region-info/-/region-info-1.204.0.tgz", "integrity": "sha512-lPkYJNoN4Gjlf0Fdfgcd1RTm5RD9qtfaFMwVvTn2KGTr7ZqmFskGQ9FqIcd5vd6GmsbAL8OrFOToLr1AHDuOiQ==", "deprecated": "AWS CDK v1 has reached End-of-Support on 2023-06-01.\nThis package is no longer being updated, and users should migrate to AWS CDK v2.\n\nFor more information on how to migrate, see https://docs.aws.amazon.com/cdk/v2/guide/migrating-v2.html", "engines": {"node": ">= 14.15.0"}}, "node_modules/@babel/code-frame": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/code-frame/-/code-frame-7.27.1.tgz", "integrity": "sha512-cjQ7ZlQ0Mv3b47hABuTevyTuYN4i+loJKGeV9flcCgIK37cCXRh+L1bd3iBHlynerhQ7BhCkn2BPbQUL+rGqFg==", "dev": true, "dependencies": {"@babel/helper-validator-identifier": "^7.27.1", "js-tokens": "^4.0.0", "picocolors": "^1.1.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/compat-data": {"version": "7.27.7", "resolved": "https://registry.npmjs.org/@babel/compat-data/-/compat-data-7.27.7.tgz", "integrity": "sha512-xgu/ySj2mTiUFmdE9yCMfBxLp4DHd5DwmbbD05YAuICfodYT3VvRxbrh81LGQ/8UpSdtMdfKMn3KouYDX59DGQ==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/core": {"version": "7.27.7", "resolved": "https://registry.npmjs.org/@babel/core/-/core-7.27.7.tgz", "integrity": "sha512-BU2f9tlKQ5CAthiMIgpzAh4eDTLWo1mqi9jqE2OxMG0E/OM199VJt2q8BztTxpnSW0i1ymdwLXRJnYzvDM5r2w==", "dev": true, "dependencies": {"@ampproject/remapping": "^2.2.0", "@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/helper-compilation-targets": "^7.27.2", "@babel/helper-module-transforms": "^7.27.3", "@babel/helpers": "^7.27.6", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/traverse": "^7.27.7", "@babel/types": "^7.27.7", "convert-source-map": "^2.0.0", "debug": "^4.1.0", "gensync": "^1.0.0-beta.2", "json5": "^2.2.3", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}, "funding": {"type": "opencollective", "url": "https://opencollective.com/babel"}}, "node_modules/@babel/generator": {"version": "7.27.5", "resolved": "https://registry.npmjs.org/@babel/generator/-/generator-7.27.5.tgz", "integrity": "sha512-ZGhA37l0e/g2s1Cnzdix0O3aLYm66eF8aufiVteOgnwxgnRP8GoyMj7VWsgWnQbVKXyge7hqrFh2K2TQM6t1Hw==", "dev": true, "dependencies": {"@babel/parser": "^7.27.5", "@babel/types": "^7.27.3", "@jridgewell/gen-mapping": "^0.3.5", "@jridgewell/trace-mapping": "^0.3.25", "jsesc": "^3.0.2"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-compilation-targets": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/helper-compilation-targets/-/helper-compilation-targets-7.27.2.tgz", "integrity": "sha512-2+1thGUUWWjLTYTHZWK1n8Yga0ijBz1XAhUXcKy81rd5g6yh7hGqMp45v7cadSbEHc9G3OTv45SyneRN3ps4DQ==", "dev": true, "dependencies": {"@babel/compat-data": "^7.27.2", "@babel/helper-validator-option": "^7.27.1", "browserslist": "^4.24.0", "lru-cache": "^5.1.1", "semver": "^6.3.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-imports": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-module-imports/-/helper-module-imports-7.27.1.tgz", "integrity": "sha512-0gSFWUPNXNopqtIPQvlD5WgXYI5GY2kP2cCvoT8kczjbfcfuIljTbcWrulD1CIPIX2gt1wghbDy08yE1p+/r3w==", "dev": true, "dependencies": {"@babel/traverse": "^7.27.1", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-module-transforms": {"version": "7.27.3", "resolved": "https://registry.npmjs.org/@babel/helper-module-transforms/-/helper-module-transforms-7.27.3.tgz", "integrity": "sha512-dSOvYwvyLsWBeIRyOeHXp5vPj5l1I011r52FM1+r1jCERv+aFXYk4whgQccYEGYxK2H3ZAIA8nuPkQ0HaUo3qg==", "dev": true, "dependencies": {"@babel/helper-module-imports": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1", "@babel/traverse": "^7.27.3"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/@babel/helper-plugin-utils": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-plugin-utils/-/helper-plugin-utils-7.27.1.tgz", "integrity": "sha512-1gn1Up5YXka3YYAHGKpbideQ5Yjf1tDa9qYcgysz+cNCXukyLl6DjPXhD3VRwSb8c0J9tA4b2+rHEZtc6R0tlw==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-string-parser": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-string-parser/-/helper-string-parser-7.27.1.tgz", "integrity": "sha512-qMlSxKbpRlAridDExk92nSobyDdpPijUq2DW6oDnUqd0iOGxmQjyqhMIihI9+zv4LPyZdRje2cavWPbCbWm3eA==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-identifier": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-identifier/-/helper-validator-identifier-7.27.1.tgz", "integrity": "sha512-D2hP9eA+Sqx1kBZgzxZh0y1trbuU+JoDkiEwqhQ36nodYqJwyEIhPSdMNd7lOm/4io72luTPWH20Yda0xOuUow==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helper-validator-option": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/helper-validator-option/-/helper-validator-option-7.27.1.tgz", "integrity": "sha512-YvjJow9FxbhFFKDSuFnVCe2WxXk1zWc22fFePVNEaWJEu8IrZVlda6N0uHwzZrUM1il7NC9Mlp4MaJYbYd9JSg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/helpers": {"version": "7.27.6", "resolved": "https://registry.npmjs.org/@babel/helpers/-/helpers-7.27.6.tgz", "integrity": "sha512-muE8Tt8M22638HU31A3CgfSUciwz1fhATfoVai05aPXGor//CdWDCbnlY1yvBPo07njuVOCNGCSp/GTt12lIug==", "dev": true, "dependencies": {"@babel/template": "^7.27.2", "@babel/types": "^7.27.6"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/parser": {"version": "7.27.7", "resolved": "https://registry.npmjs.org/@babel/parser/-/parser-7.27.7.tgz", "integrity": "sha512-qnzXzDXdr/po3bOTbTIQZ7+TxNKxpkN5IifVLXS+r7qwynkZfPyjZfE7hCXbo7IoO9TNcSyibgONsf2HauUd3Q==", "dev": true, "dependencies": {"@babel/types": "^7.27.7"}, "bin": {"parser": "bin/babel-parser.js"}, "engines": {"node": ">=6.0.0"}}, "node_modules/@babel/plugin-syntax-async-generators": {"version": "7.8.4", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-async-generators/-/plugin-syntax-async-generators-7.8.4.tgz", "integrity": "sha512-tycmZxkGfZaxhMRbXlPXuVFpdWlXpir2W4AMhSJgRKzk/eDlIXOhb2LHWoLpDF7TEHylV5zNhykX6KAgHJmTNw==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-bigint": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-bigint/-/plugin-syntax-bigint-7.8.3.tgz", "integrity": "sha512-wnTnFlG+YxQm3vDxpGE57Pj0srRU4sHE/mDkt1qv2YJJSeUAec2ma4WLUnUPeKjyrfntVwe/N6dCXpU+zL3Npg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-properties": {"version": "7.12.13", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-properties/-/plugin-syntax-class-properties-7.12.13.tgz", "integrity": "sha512-fm4idjKla0YahUNgFNLCB0qySdsoPiZP3iQE3rky0mBUtMZ23yDJ9SJdg6dXTSDnulOVqiF3Hgr9nbXvXTQZYA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.12.13"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-class-static-block": {"version": "7.14.5", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-class-static-block/-/plugin-syntax-class-static-block-7.14.5.tgz", "integrity": "sha512-b+YyPmr6ldyNnM6sqYeMWE+bgJcJpO6yS4QD7ymxgH34GBPNDM/THBh8iunyvKIZztiwLH4CJZ0RxTk9emgpjw==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-attributes": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-attributes/-/plugin-syntax-import-attributes-7.27.1.tgz", "integrity": "sha512-oFT0FrKHgF53f4vOsZGi2Hh3I35PfSmVs4IBFLFj4dnafP+hIWDLg3VyKmUHfLoLHlyxY4C7DGtmHuJgn+IGww==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-import-meta": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-import-meta/-/plugin-syntax-import-meta-7.10.4.tgz", "integrity": "sha512-Yqfm+XDx0+Prh3VSeEQCPU81yC+JWZ2pDPFSS4ZdpfZhp4MkFMaDC1UqseovEKwSUpnIL7+vK+Clp7bfh0iD7g==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-json-strings": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-json-strings/-/plugin-syntax-json-strings-7.8.3.tgz", "integrity": "sha512-lY6kdGpWHvjoe2vk4WrAapEuBR69EMxZl+RoGRhrFGNYVK8mOPAW8VfbT/ZgrFbXlDNiiaxQnAtgVCZ6jv30EA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-jsx": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-jsx/-/plugin-syntax-jsx-7.27.1.tgz", "integrity": "sha512-y8YTNIeKoyhGd9O0Jiyzyyqk8gdjnumGTQPsz0xOZOQ2RmkVJeZ1vmmfIvFEKqucBG6axJGBZDE/7iI5suUI/w==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-logical-assignment-operators": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-logical-assignment-operators/-/plugin-syntax-logical-assignment-operators-7.10.4.tgz", "integrity": "sha512-d8waShlpFDinQ5MtvGU9xDAOzKH47+FFoney2baFIoMr952hKOLp1HR7VszoZvOsV/4+RRszNY7D17ba0te0ig==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-nullish-coalescing-operator": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-nullish-coalescing-operator/-/plugin-syntax-nullish-coalescing-operator-7.8.3.tgz", "integrity": "sha512-aSff4zPII1u2QD7y+F8oDsz19ew4IGEJg9SVW+bqwpwtfFleiQDMdzA/R+UlWDzfnHFCxxleFT0PMIrR36XLNQ==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-numeric-separator": {"version": "7.10.4", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-numeric-separator/-/plugin-syntax-numeric-separator-7.10.4.tgz", "integrity": "sha512-9H6YdfkcK/uOnY/K7/aA2xpzaAgkQn37yzWUMRK7OaPOqOpGS1+n0H5hxT9AUw9EsSjPW8SVyMJwYRtWs3X3ug==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.10.4"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-object-rest-spread": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-object-rest-spread/-/plugin-syntax-object-rest-spread-7.8.3.tgz", "integrity": "sha512-XoqMijGZb9y3y2XskN+P1wUGiVwWZ5JmoDRwx5+3GmEplNyVM2s2Dg8ILFQm8rWM48orGy5YpI5Bl8U1y7ydlA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-catch-binding": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-catch-binding/-/plugin-syntax-optional-catch-binding-7.8.3.tgz", "integrity": "sha512-6VPD0Pc1lpTqw0aKoeRTMiB+kWhAoT24PA+ksWSBrFtl5SIRVpZlwN3NNPQjehA2E/91FV3RjLWoVTglWcSV3Q==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-optional-chaining": {"version": "7.8.3", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-optional-chaining/-/plugin-syntax-optional-chaining-7.8.3.tgz", "integrity": "sha512-KoK9ErH1MBlCPxV0VANkXW2/dw4vlbGDrFgz8bmUsBGYkFRcbRwMh6cIJubdPrkxRwuGdtCk0v/wPTKbQgBjkg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.8.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-private-property-in-object": {"version": "7.14.5", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-private-property-in-object/-/plugin-syntax-private-property-in-object-7.14.5.tgz", "integrity": "sha512-0wVnp9dxJ72ZUJDV27ZfbSj6iHLoytYZmh3rFcxNnvsJF3ktkzLDZPy/mA17HGsaQT3/DQsWYX1f1QGWkCoVUg==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-top-level-await": {"version": "7.14.5", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-top-level-await/-/plugin-syntax-top-level-await-7.14.5.tgz", "integrity": "sha512-hx++upLv5U1rgYfwe1xBQUhRmU41NEvpUvrp8jkrSCdvGSnM5/qdRMtylJ6PG5OFkBaHkbTAKTnd3/YyESRHFw==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.14.5"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/plugin-syntax-typescript": {"version": "7.27.1", "resolved": "https://registry.npmjs.org/@babel/plugin-syntax-typescript/-/plugin-syntax-typescript-7.27.1.tgz", "integrity": "sha512-xfYCBMxveHrRMnAWl1ZlPXOZjzkN82THFvLhQhFXFt81Z5HnN+EtUkZhv/zcKpmT3fzmWZB0ywiBrbC3vogbwQ==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.27.1"}, "engines": {"node": ">=6.9.0"}, "peerDependencies": {"@babel/core": "^7.0.0-0"}}, "node_modules/@babel/template": {"version": "7.27.2", "resolved": "https://registry.npmjs.org/@babel/template/-/template-7.27.2.tgz", "integrity": "sha512-LPDZ85aEJyYSd18/DkjNh4/y1ntkE5KwUHWTiqgRxruuZL2F1yuHligVHLvcHY2vMHXttKFpJn6LwfI7cw7ODw==", "dev": true, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/parser": "^7.27.2", "@babel/types": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/traverse": {"version": "7.27.7", "resolved": "https://registry.npmjs.org/@babel/traverse/-/traverse-7.27.7.tgz", "integrity": "sha512-X6ZlfR/O/s5EQ/SnUSLzr+6kGnkg8HXGMzpgsMsrJVcfDtH1vIp6ctCN4eZ1LS5c0+te5Cb6Y514fASjMRJ1nw==", "dev": true, "dependencies": {"@babel/code-frame": "^7.27.1", "@babel/generator": "^7.27.5", "@babel/parser": "^7.27.7", "@babel/template": "^7.27.2", "@babel/types": "^7.27.7", "debug": "^4.3.1", "globals": "^11.1.0"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@babel/types": {"version": "7.27.7", "resolved": "https://registry.npmjs.org/@babel/types/-/types-7.27.7.tgz", "integrity": "sha512-8OLQgDScAOHXnAz2cV+RfzzNMipuLVBz2biuAJFMV9bfkNf393je3VM8CLkjQodW5+iWsSJdSgSWT6rsZoXHPw==", "dev": true, "dependencies": {"@babel/helper-string-parser": "^7.27.1", "@babel/helper-validator-identifier": "^7.27.1"}, "engines": {"node": ">=6.9.0"}}, "node_modules/@bcoe/v8-coverage": {"version": "0.2.3", "resolved": "https://registry.npmjs.org/@bcoe/v8-coverage/-/v8-coverage-0.2.3.tgz", "integrity": "sha512-0hYQ8SB4Db5zvZB4axdMHGwEaQjkZzFjQiN9LVYvIFB2nSUHW9tYpxWriPrWDASIxiaXax83REcLxuSdnGPZtw==", "dev": true}, "node_modules/@cspotcode/source-map-support": {"version": "0.8.1", "resolved": "https://registry.npmjs.org/@cspotcode/source-map-support/-/source-map-support-0.8.1.tgz", "integrity": "sha512-IchNf6dN4tHoMFIn/7OE8LWZ19Y6q/67Bmf6vnGREv8RSbBVb9LPJxEcnwrcwX6ixSvaiGoomAUvu4YSxXrVgw==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "0.3.9"}, "engines": {"node": ">=12"}}, "node_modules/@cspotcode/source-map-support/node_modules/@jridgewell/trace-mapping": {"version": "0.3.9", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.9.tgz", "integrity": "sha512-3Belt6tdc8bPgAtbcmdtNJlirVoTmEb5e2gC94PnkwEW9jI6CAHUeoG85tjWP5WquqfavoMtMwiG4P926ZKKuQ==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "^3.0.3", "@jridgewell/sourcemap-codec": "^1.4.10"}}, "node_modules/@istanbuljs/load-nyc-config": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/@istanbuljs/load-nyc-config/-/load-nyc-config-1.1.0.tgz", "integrity": "sha512-VjeHSlIzpv/NyD3N0YuHfXOPDIixcA1q2ZV98wsMqcYlPmv2n3Yb2lYP9XMElnaFVXg5A7YLTeLu6V84uQDjmQ==", "dev": true, "dependencies": {"camelcase": "^5.3.1", "find-up": "^4.1.0", "get-package-type": "^0.1.0", "js-yaml": "^3.13.1", "resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/@istanbuljs/schema": {"version": "0.1.3", "resolved": "https://registry.npmjs.org/@istanbuljs/schema/-/schema-0.1.3.tgz", "integrity": "sha512-ZXRY4jNvVgSVQ8DL3LTcakaAtXwTVUxE81hslsyD2AtoXW/wVob10HkOJ1X/pAlcI7D+2YoZKg5do8G/w6RYgA==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/@jest/console": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/console/-/console-29.7.0.tgz", "integrity": "sha512-5Ni4CU7XHQi32IJ398EEP4RrB8eV09sXP2ROqD4bksHrnTree52PsxvX8tpL8LvTZ3pFzXyPbNQReSN41CAhOg==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/core": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/core/-/core-29.7.0.tgz", "integrity": "sha512-n7aeXWKMnGtDA48y8TLWJPJmLmmZ642Ceo78cYWEpiD7FzDgmNDV/GCVRorPABdXLJZ/9wzzgZAlHjXjxDHGsg==", "dev": true, "dependencies": {"@jest/console": "^29.7.0", "@jest/reporters": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "ci-info": "^3.2.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-changed-files": "^29.7.0", "jest-config": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-resolve-dependencies": "^29.7.0", "jest-runner": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "jest-watcher": "^29.7.0", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-ansi": "^6.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/environment": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/environment/-/environment-29.7.0.tgz", "integrity": "sha512-aQIfHDq33ExsN4jP1NWGXhxgQ/wixs60gDiKO+XVMd8Mn0NWPWgc34ZQDTb2jKaUWQ7MuwoitXAsN2XVXNMpAw==", "dev": true, "dependencies": {"@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/expect/-/expect-29.7.0.tgz", "integrity": "sha512-8uMeAMycttpva3P1lBHB8VciS9V0XAr3GymPpipdyQXbBcuhkLQOSe8E/p92RyAdToS6ZD1tFkX+CkhoECE0dQ==", "dev": true, "dependencies": {"expect": "^29.7.0", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/expect-utils": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/expect-utils/-/expect-utils-29.7.0.tgz", "integrity": "sha512-GlsNBWiFQFCVi9QVSx7f5AgMeLxe9YCCs5PuP2O2LdjDAA8Jh9eX7lA1Jq/xdXw3Wb3hyvlFNfZIfcRetSzYcA==", "dev": true, "dependencies": {"jest-get-type": "^29.6.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/fake-timers": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/fake-timers/-/fake-timers-29.7.0.tgz", "integrity": "sha512-q4DH1Ha4TTFPdxLsqDXK1d3+ioSL7yL5oCMJZgDYm6i+6CygW5E5xVr/D1HdsGxjt1ZWSfUAs9OxSB/BNelWrQ==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "@sinonjs/fake-timers": "^10.0.2", "@types/node": "*", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/globals": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/globals/-/globals-29.7.0.tgz", "integrity": "sha512-mpiz3dutLbkW2MNFubUGUEVLkTGiqW6yLVTA+JbP6fI6J5iL9Y0Nlg8k95pcF8ctKwCS7WVxteBs29hhfAotzQ==", "dev": true, "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/types": "^29.6.3", "jest-mock": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/reporters": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/reporters/-/reporters-29.7.0.tgz", "integrity": "sha512-DApq0KJbJOEzAFYjHADNNxAE3KbhxQB1y5Kplb5Waqw6zVbuWatSnMjE5gs8FUgEPmNsnZA3NCWl9NG0ia04Pg==", "dev": true, "dependencies": {"@bcoe/v8-coverage": "^0.2.3", "@jest/console": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "@types/node": "*", "chalk": "^4.0.0", "collect-v8-coverage": "^1.0.0", "exit": "^0.1.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "istanbul-lib-coverage": "^3.0.0", "istanbul-lib-instrument": "^6.0.0", "istanbul-lib-report": "^3.0.0", "istanbul-lib-source-maps": "^4.0.0", "istanbul-reports": "^3.1.3", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "slash": "^3.0.0", "string-length": "^4.0.1", "strip-ansi": "^6.0.0", "v8-to-istanbul": "^9.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/@jest/schemas": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/@jest/schemas/-/schemas-29.6.3.tgz", "integrity": "sha512-mo5j5X+jIZmJQveBKeS/clAueipV7KgiX1vMgCxam1RNYiqE1w62n0/tJJnHtjW8ZHcQco5gY85jA3mi0L+nSA==", "dev": true, "dependencies": {"@sinclair/typebox": "^0.27.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/source-map": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/@jest/source-map/-/source-map-29.6.3.tgz", "integrity": "sha512-MHjT95QuipcPrpLM+8JMSzFx6eHp5Bm+4XeFDJlwsvVBjmKNiIAvasGK2fxz2WbGRlnvqehFbh07MMa7n3YJnw==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.18", "callsites": "^3.0.0", "graceful-fs": "^4.2.9"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-result": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/test-result/-/test-result-29.7.0.tgz", "integrity": "sha512-Fdx+tv6x1zlkJPcWXmMDAG2HBnaR9XPSd5aDWQVsfrZmLVT3lU1cwyxLgRmXR9yrq4NBoEm9BMsfgFzTQAbJYA==", "dev": true, "dependencies": {"@jest/console": "^29.7.0", "@jest/types": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "collect-v8-coverage": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/test-sequencer": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/test-sequencer/-/test-sequencer-29.7.0.tgz", "integrity": "sha512-GQwJ5WZVrKnOJuiYiAF52UNUJXgTZx1NHjFSEB0qEMmSZKAkdMoIzw/Cj6x6NF4AvV23AUqDpFzQkN/eYCYTxw==", "dev": true, "dependencies": {"@jest/test-result": "^29.7.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/transform": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/@jest/transform/-/transform-29.7.0.tgz", "integrity": "sha512-ok/BTPFzFKVMwO5eOHRrvnBVHdRy9IrsrW1GpMaQ9MCnilNLXQKmAX8s1YXDFaai9xJpac2ySzV0YeRRECr2Vw==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@jest/types": "^29.6.3", "@jridgewell/trace-mapping": "^0.3.18", "babel-plugin-istanbul": "^6.1.1", "chalk": "^4.0.0", "convert-source-map": "^2.0.0", "fast-json-stable-stringify": "^2.1.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "micromatch": "^4.0.4", "pirates": "^4.0.4", "slash": "^3.0.0", "write-file-atomic": "^4.0.2"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jest/types": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/@jest/types/-/types-29.6.3.tgz", "integrity": "sha512-u3UPsIilWKOM3F9CXtrG8LEJmNxwoCQC/XVj4IKYXvvpx7QIi/Kg1LI5uDmDpKlac62NUtX7eLjRh+jVZcLOzw==", "dev": true, "dependencies": {"@jest/schemas": "^29.6.3", "@types/istanbul-lib-coverage": "^2.0.0", "@types/istanbul-reports": "^3.0.0", "@types/node": "*", "@types/yargs": "^17.0.8", "chalk": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/@jridgewell/gen-mapping": {"version": "0.3.10", "resolved": "https://registry.npmjs.org/@jridgewell/gen-mapping/-/gen-mapping-0.3.10.tgz", "integrity": "sha512-HM2F4B9N4cA0RH2KQiIZOHAZqtP4xGS4IZ+SFe1SIbO4dyjf9MTY2Bo3vHYnm0hglWfXqBrzUBSa+cJfl3Xvrg==", "dev": true, "dependencies": {"@jridgewell/sourcemap-codec": "^1.5.0", "@jridgewell/trace-mapping": "^0.3.24"}}, "node_modules/@jridgewell/resolve-uri": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/@jridgewell/resolve-uri/-/resolve-uri-3.1.2.tgz", "integrity": "sha512-bRISgCIjP20/tbWSPWMEi54QVPRZExkuD9lJL+UIxUKtwVJA8wW1Trb1jMs1RFXo1CBTNZ/5hpC9QvmKWdopKw==", "dev": true, "engines": {"node": ">=6.0.0"}}, "node_modules/@jridgewell/sourcemap-codec": {"version": "1.5.2", "resolved": "https://registry.npmjs.org/@jridgewell/sourcemap-codec/-/sourcemap-codec-1.5.2.tgz", "integrity": "sha512-gKYheCylLIedI+CSZoDtGkFV9YEBxRRVcfCH7OfAqh4TyUyRjEE6WVE/aXDXX0p8BIe/QgLcaAoI0220KRRFgg==", "dev": true}, "node_modules/@jridgewell/trace-mapping": {"version": "0.3.27", "resolved": "https://registry.npmjs.org/@jridgewell/trace-mapping/-/trace-mapping-0.3.27.tgz", "integrity": "sha512-VO95AxtSFMelbg3ouljAYnfvTEwSWVt/2YLf+U5Ejd8iT5mXE2Sa/1LGyvySMne2CGsepGLI7KpF3EzE3Aq9Mg==", "dev": true, "dependencies": {"@jridgewell/resolve-uri": "^3.1.0", "@jridgewell/sourcemap-codec": "^1.4.14"}}, "node_modules/@sinclair/typebox": {"version": "0.27.8", "resolved": "https://registry.npmjs.org/@sinclair/typebox/-/typebox-0.27.8.tgz", "integrity": "sha512-+Fj43pSMwJs4KRrH/938Uf+uAELIgVBmQzg/q1YG10djyfA3TnrU8N8XzqCh/okZdszqBQTZf96idMfE5lnwTA==", "dev": true}, "node_modules/@sinonjs/commons": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/@sinonjs/commons/-/commons-3.0.1.tgz", "integrity": "sha512-K3mCHKQ9sVh8o1C9cxkwxaOmXoAMlDxC1mYyHrjqOWEcBjYr76t96zL2zlj5dUGZ3HSw240X1qgH3Mjf1yJWpQ==", "dev": true, "dependencies": {"type-detect": "4.0.8"}}, "node_modules/@sinonjs/fake-timers": {"version": "10.3.0", "resolved": "https://registry.npmjs.org/@sinonjs/fake-timers/-/fake-timers-10.3.0.tgz", "integrity": "sha512-V4BG07kuYSUkTCSBHG8G8TNhM+F19jXFWnQtzj+we8DrkpSBCee9Z3Ms8yiGer/dlmhe35/Xdgyo3/0rQKg7YA==", "dev": true, "dependencies": {"@sinonjs/commons": "^3.0.0"}}, "node_modules/@tsconfig/node10": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node10/-/node10-1.0.11.tgz", "integrity": "sha512-DcRjDCujK/kCk/cUe8Xz8ZSpm8mS3mNNpta+jGCA6USEDfktlNvm1+IuZ9eTcDbNk41BHwpHHeW+N1lKCz4zOw==", "dev": true}, "node_modules/@tsconfig/node12": {"version": "1.0.11", "resolved": "https://registry.npmjs.org/@tsconfig/node12/-/node12-1.0.11.tgz", "integrity": "sha512-cqefuRsh12pWyGsIoBKJA9luFu3mRxCA+ORZvA4ktLSzIuCUtWVxGIuXigEwO5/ywWFMZ2QEGKWvkZG1zDMTag==", "dev": true}, "node_modules/@tsconfig/node14": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/@tsconfig/node14/-/node14-1.0.3.tgz", "integrity": "sha512-ysT8mhdixWK6Hw3i1V2AeRqZ5WfXg1G43mqoYlM2nc6388Fq5jcXyr5mRsqViLx/GJYdoL0bfXD8nmF+Zn/Iow==", "dev": true}, "node_modules/@tsconfig/node16": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/@tsconfig/node16/-/node16-1.0.4.tgz", "integrity": "sha512-vxhUy4J8lyeyinH7Azl1pdd43GJhZH/tP2weN8TntQblOY+A0XbT8DJk1/oCPuOOyg/Ja757rG0CgHcWC8OfMA==", "dev": true}, "node_modules/@types/babel__core": {"version": "7.20.5", "resolved": "https://registry.npmjs.org/@types/babel__core/-/babel__core-7.20.5.tgz", "integrity": "sha512-qoQprZvz5wQFJwMDqeseRXWv3rqMvhgpbXFfVyWhbx9X47POIA6i/+dXefEmZKoAgOaTdaIgNSMqMIU61yRyzA==", "dev": true, "dependencies": {"@babel/parser": "^7.20.7", "@babel/types": "^7.20.7", "@types/babel__generator": "*", "@types/babel__template": "*", "@types/babel__traverse": "*"}}, "node_modules/@types/babel__generator": {"version": "7.27.0", "resolved": "https://registry.npmjs.org/@types/babel__generator/-/babel__generator-7.27.0.tgz", "integrity": "sha512-ufFd2Xi92OAVPYsy+P4n7/U7e68fex0+Ee8gSG9KX7eo084CWiQ4sdxktvdl0bOPupXtVJPY19zk6EwWqUQ8lg==", "dev": true, "dependencies": {"@babel/types": "^7.0.0"}}, "node_modules/@types/babel__template": {"version": "7.4.4", "resolved": "https://registry.npmjs.org/@types/babel__template/-/babel__template-7.4.4.tgz", "integrity": "sha512-h/NUaSyG5EyxBIp8YRxo4RMe2/qQgvyowRwVMzhYhBCONbW8PUsg4lkFMrhgZhUe5z3L3MiLDuvyJ/CaPa2A8A==", "dev": true, "dependencies": {"@babel/parser": "^7.1.0", "@babel/types": "^7.0.0"}}, "node_modules/@types/babel__traverse": {"version": "7.20.7", "resolved": "https://registry.npmjs.org/@types/babel__traverse/-/babel__traverse-7.20.7.tgz", "integrity": "sha512-dkO5fhS7+/oos4ciWxyEyjWe48zmG6wbCheo/G2ZnHx4fs3EU6YC6UM8rk56gAjNJ9P3MTH2jo5jb92/K6wbng==", "dev": true, "dependencies": {"@babel/types": "^7.20.7"}}, "node_modules/@types/graceful-fs": {"version": "4.1.9", "resolved": "https://registry.npmjs.org/@types/graceful-fs/-/graceful-fs-4.1.9.tgz", "integrity": "sha512-olP3sd1qOEe5dXTSaFvQG+02VdRXcdytWLAZsAq1PecU8uqQAhkrnbli7DagjtXKW/Bl7YJbUsa8MPcuc8LHEQ==", "dev": true, "dependencies": {"@types/node": "*"}}, "node_modules/@types/istanbul-lib-coverage": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/@types/istanbul-lib-coverage/-/istanbul-lib-coverage-2.0.6.tgz", "integrity": "sha512-2QF/t/auWm0lsy8XtKVPG19v3sSOQlJe/YHZgfjb/KBBHOGSV+J2q/S671rcq9uTBrLAXmZpqJiaQbMT+zNU1w==", "dev": true}, "node_modules/@types/istanbul-lib-report": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/@types/istanbul-lib-report/-/istanbul-lib-report-3.0.3.tgz", "integrity": "sha512-NQn7AHQnk/RSLOxrBbGyJM/aVQ+pjj5HCgasFxc0K/KhoATfQ/47AyUl15I2yBUpihjmas+a+VJBOqecrFH+uA==", "dev": true, "dependencies": {"@types/istanbul-lib-coverage": "*"}}, "node_modules/@types/istanbul-reports": {"version": "3.0.4", "resolved": "https://registry.npmjs.org/@types/istanbul-reports/-/istanbul-reports-3.0.4.tgz", "integrity": "sha512-pk2B1NWalF9toCRu6gjBzR69syFjP4Od8WRAX+0mmf9lAjCRicLOWc+ZrxZHx/0XRjotgkF9t6iaMJ+aXcOdZQ==", "dev": true, "dependencies": {"@types/istanbul-lib-report": "*"}}, "node_modules/@types/jest": {"version": "29.5.14", "resolved": "https://registry.npmjs.org/@types/jest/-/jest-29.5.14.tgz", "integrity": "sha512-ZN+4sdnLUbo8EVvVc2ao0GFW6oVrQRPn4K2lglySj7APvSrgzxHiNNK99us4WDMi57xxA2yggblIAMNhXOotLQ==", "dev": true, "dependencies": {"expect": "^29.0.0", "pretty-format": "^29.0.0"}}, "node_modules/@types/node": {"version": "22.7.9", "resolved": "https://registry.npmjs.org/@types/node/-/node-22.7.9.tgz", "integrity": "sha512-jrTfRC7FM6nChvU7X2KqcrgquofrWLFDeYC1hKfwNWomVvrn7JIksqf344WN2X/y8xrgqBd2dJATZV4GbatBfg==", "dev": true, "dependencies": {"undici-types": "~6.19.2"}}, "node_modules/@types/stack-utils": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/@types/stack-utils/-/stack-utils-2.0.3.tgz", "integrity": "sha512-9aEbYZ3TbYMznPdcdr3SmIrLXwC/AKZXQeCf9Pgao5CKb8CyHuEX5jzWPTkvregvhRJHcpRO6BFoGW9ycaOkYw==", "dev": true}, "node_modules/@types/yargs": {"version": "17.0.33", "resolved": "https://registry.npmjs.org/@types/yargs/-/yargs-17.0.33.tgz", "integrity": "sha512-WpxBCKWPLr4xSsHgz511rFJAM+wS28w2zEO1QDNY5zM/S8ok70NNfztH0xwhqKyaK0OHCbN98LDAZuy1ctxDkA==", "dev": true, "dependencies": {"@types/yargs-parser": "*"}}, "node_modules/@types/yargs-parser": {"version": "21.0.3", "resolved": "https://registry.npmjs.org/@types/yargs-parser/-/yargs-parser-21.0.3.tgz", "integrity": "sha512-I4q9QU9MQv4oEOz4tAHJtNz1cwuLxn2F3xcc2iV5WdqLPpUnj30aUuxt1mAxYTG+oe8CZMV/+6rU4S4gRDzqtQ==", "dev": true}, "node_modules/acorn": {"version": "8.15.0", "resolved": "https://registry.npmjs.org/acorn/-/acorn-8.15.0.tgz", "integrity": "sha512-NZyJarBfL7nWwIq+FDL6Zp/yHEhePMNnnJ0y3qfieCrmNvYct8uvtiV41UvlSe6apAfk0fY1FbWx+NwfmpvtTg==", "dev": true, "bin": {"acorn": "bin/acorn"}, "engines": {"node": ">=0.4.0"}}, "node_modules/acorn-walk": {"version": "8.3.4", "resolved": "https://registry.npmjs.org/acorn-walk/-/acorn-walk-8.3.4.tgz", "integrity": "sha512-ueEepnujpqee2o5aIYnvHU6C0A42MNdsIDeqy5BydrkuC5R1ZuUFnm27EeFJGoEHJQgn3uleRvmTXaJgfXbt4g==", "dev": true, "dependencies": {"acorn": "^8.11.0"}, "engines": {"node": ">=0.4.0"}}, "node_modules/ansi-escapes": {"version": "4.3.2", "resolved": "https://registry.npmjs.org/ansi-escapes/-/ansi-escapes-4.3.2.tgz", "integrity": "sha512-gKXj5ALrKWQLsYG9jlTRmR/xKluxHV+Z9QEwNIgCfM1/uwPMCuzVVnh5mwTd+OuBZcwSIMbqssNWRm1lE51QaQ==", "dev": true, "dependencies": {"type-fest": "^0.21.3"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ansi-regex": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/ansi-regex/-/ansi-regex-5.0.1.tgz", "integrity": "sha512-quJQXlTSUGL2LH9SUXo8VwsY4soanhgo6LNSm84E1LBcE8s3O0wpdiRzyR9z/ZZJMlMWv37qOOb9pdJlMUEKFQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/ansi-styles": {"version": "4.3.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-4.3.0.tgz", "integrity": "sha512-zbB9rCJAT1rbjiVDb2hqKFHNYLxgtk8NURxZ3IZwD3F6NtxbXZQCnnSi1Lkx+IDohdPlFp222wVALIheZJQSEg==", "dev": true, "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/anymatch": {"version": "3.1.3", "resolved": "https://registry.npmjs.org/anymatch/-/anymatch-3.1.3.tgz", "integrity": "sha512-KMReFUr0B4t+D+OBkjR3KYqvocp2XaSzO55UcB6mgQMd3KbcE+mWTyvVV7D/zsdEbNnV6acZUutkiHQXvTr1Rw==", "dev": true, "dependencies": {"normalize-path": "^3.0.0", "picomatch": "^2.0.4"}, "engines": {"node": ">= 8"}}, "node_modules/arg": {"version": "4.1.3", "resolved": "https://registry.npmjs.org/arg/-/arg-4.1.3.tgz", "integrity": "sha512-58S9QDqG0Xx27YwPSt9fJxivjYl432YCwfDMfZ+71RAqUrZef7LrKQZ3LHLOwCS4FLNBplP533Zx895SeOCHvA==", "dev": true}, "node_modules/argparse": {"version": "1.0.10", "resolved": "https://registry.npmjs.org/argparse/-/argparse-1.0.10.tgz", "integrity": "sha512-o5Roy6tNG4SL/FOkCAN6RzjiakZS25RLYFrcMttJqbdd8BWrnA+fGz57iN5Pb06pvBGvl5gQ0B48dJlslXvoTg==", "dev": true, "dependencies": {"sprintf-js": "~1.0.2"}}, "node_modules/async": {"version": "3.2.6", "resolved": "https://registry.npmjs.org/async/-/async-3.2.6.tgz", "integrity": "sha512-htCUDlxyyCLMgaM3xXg0C0LW2xqfuQ6p05pCEIsXuyQ+a1koYKTuBMzRNwmybfLgvJDMd0r1LTn4+E0Ti6C2AA==", "dev": true}, "node_modules/aws-cdk": {"version": "2.1016.1", "resolved": "https://registry.npmjs.org/aws-cdk/-/aws-cdk-2.1016.1.tgz", "integrity": "sha512-248TBiluT8jHUjkpzvWJOHv2fS+An9fiII3eji8H7jwfTu5yMBk7on4B/AVNr9A1GXJk9I32qf9Q0A3rLWRYPQ==", "dev": true, "bin": {"cdk": "bin/cdk"}, "engines": {"node": ">= 14.15.0"}, "optionalDependencies": {"fsevents": "2.3.2"}}, "node_modules/aws-cdk-lib": {"version": "2.196.0", "resolved": "https://registry.npmjs.org/aws-cdk-lib/-/aws-cdk-lib-2.196.0.tgz", "integrity": "sha512-CAy0ahDpUHuYArP0vhDrOZ9OUSyRAznCcKcbETJOPr438QZUCyv+tRVgk8VP5lFW1JuV9sNzIvxdZcsNVSCF7g==", "bundleDependencies": ["@balena/dockerignore", "case", "fs-extra", "ignore", "jsonschema", "minimatch", "punycode", "semver", "table", "yaml", "mime-types"], "dependencies": {"@aws-cdk/asset-awscli-v1": "2.2.236", "@aws-cdk/asset-node-proxy-agent-v6": "^2.1.0", "@aws-cdk/cloud-assembly-schema": "^41.2.0", "@balena/dockerignore": "^1.0.2", "case": "1.6.3", "fs-extra": "^11.3.0", "ignore": "^5.3.2", "jsonschema": "^1.5.0", "mime-types": "^2.1.35", "minimatch": "^3.1.2", "punycode": "^2.3.1", "semver": "^7.7.2", "table": "^6.9.0", "yaml": "1.10.2"}, "engines": {"node": ">= 14.15.0"}, "peerDependencies": {"constructs": "^10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/@balena/dockerignore": {"version": "1.0.2", "inBundle": true, "license": "Apache-2.0"}, "node_modules/aws-cdk-lib/node_modules/ajv": {"version": "8.17.1", "inBundle": true, "license": "MIT", "dependencies": {"fast-deep-equal": "^3.1.3", "fast-uri": "^3.0.1", "json-schema-traverse": "^1.0.0", "require-from-string": "^2.0.2"}, "funding": {"type": "github", "url": "https://github.com/sponsors/epoberezkin"}}, "node_modules/aws-cdk-lib/node_modules/ansi-regex": {"version": "5.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/ansi-styles": {"version": "4.3.0", "inBundle": true, "license": "MIT", "dependencies": {"color-convert": "^2.0.1"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/aws-cdk-lib/node_modules/astral-regex": {"version": "2.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/balanced-match": {"version": "1.0.2", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/brace-expansion": {"version": "1.1.11", "inBundle": true, "license": "MIT", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/aws-cdk-lib/node_modules/case": {"version": "1.6.3", "inBundle": true, "license": "(MIT OR GPL-3.0-or-later)", "engines": {"node": ">= 0.8.0"}}, "node_modules/aws-cdk-lib/node_modules/color-convert": {"version": "2.0.1", "inBundle": true, "license": "MIT", "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/aws-cdk-lib/node_modules/color-name": {"version": "1.1.4", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/concat-map": {"version": "0.0.1", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/emoji-regex": {"version": "8.0.0", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/fast-deep-equal": {"version": "3.1.3", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/fast-uri": {"version": "3.0.6", "funding": [{"type": "github", "url": "https://github.com/sponsors/fastify"}, {"type": "opencollective", "url": "https://opencollective.com/fastify"}], "inBundle": true, "license": "BSD-3-<PERSON><PERSON>"}, "node_modules/aws-cdk-lib/node_modules/fs-extra": {"version": "11.3.0", "inBundle": true, "license": "MIT", "dependencies": {"graceful-fs": "^4.2.0", "jsonfile": "^6.0.1", "universalify": "^2.0.0"}, "engines": {"node": ">=14.14"}}, "node_modules/aws-cdk-lib/node_modules/graceful-fs": {"version": "4.2.11", "inBundle": true, "license": "ISC"}, "node_modules/aws-cdk-lib/node_modules/ignore": {"version": "5.3.2", "inBundle": true, "license": "MIT", "engines": {"node": ">= 4"}}, "node_modules/aws-cdk-lib/node_modules/is-fullwidth-code-point": {"version": "3.0.0", "inBundle": true, "license": "MIT", "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/json-schema-traverse": {"version": "1.0.0", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/jsonfile": {"version": "6.1.0", "inBundle": true, "license": "MIT", "dependencies": {"universalify": "^2.0.0"}, "optionalDependencies": {"graceful-fs": "^4.1.6"}}, "node_modules/aws-cdk-lib/node_modules/jsonschema": {"version": "1.5.0", "inBundle": true, "license": "MIT", "engines": {"node": "*"}}, "node_modules/aws-cdk-lib/node_modules/lodash.truncate": {"version": "4.4.2", "inBundle": true, "license": "MIT"}, "node_modules/aws-cdk-lib/node_modules/mime-db": {"version": "1.52.0", "inBundle": true, "license": "MIT", "engines": {"node": ">= 0.6"}}, "node_modules/aws-cdk-lib/node_modules/mime-types": {"version": "2.1.35", "inBundle": true, "license": "MIT", "dependencies": {"mime-db": "1.52.0"}, "engines": {"node": ">= 0.6"}}, "node_modules/aws-cdk-lib/node_modules/minimatch": {"version": "3.1.2", "inBundle": true, "license": "ISC", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/aws-cdk-lib/node_modules/punycode": {"version": "2.3.1", "inBundle": true, "license": "MIT", "engines": {"node": ">=6"}}, "node_modules/aws-cdk-lib/node_modules/require-from-string": {"version": "2.0.2", "inBundle": true, "license": "MIT", "engines": {"node": ">=0.10.0"}}, "node_modules/aws-cdk-lib/node_modules/semver": {"version": "7.7.2", "inBundle": true, "license": "ISC", "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/aws-cdk-lib/node_modules/slice-ansi": {"version": "4.0.0", "inBundle": true, "license": "MIT", "dependencies": {"ansi-styles": "^4.0.0", "astral-regex": "^2.0.0", "is-fullwidth-code-point": "^3.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/slice-ansi?sponsor=1"}}, "node_modules/aws-cdk-lib/node_modules/string-width": {"version": "4.2.3", "inBundle": true, "license": "MIT", "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/strip-ansi": {"version": "6.0.1", "inBundle": true, "license": "MIT", "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/aws-cdk-lib/node_modules/table": {"version": "6.9.0", "inBundle": true, "license": "BSD-3-<PERSON><PERSON>", "dependencies": {"ajv": "^8.0.1", "lodash.truncate": "^4.4.2", "slice-ansi": "^4.0.0", "string-width": "^4.2.3", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/universalify": {"version": "2.0.1", "inBundle": true, "license": "MIT", "engines": {"node": ">= 10.0.0"}}, "node_modules/aws-cdk-lib/node_modules/yaml": {"version": "1.10.2", "inBundle": true, "license": "ISC", "engines": {"node": ">= 6"}}, "node_modules/babel-jest": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/babel-jest/-/babel-jest-29.7.0.tgz", "integrity": "sha512-BrvGY3xZSwEcCzKvKsCi2GgHqDqsYkOP4/by5xCgIwGXQxIEh+8ew3gmrE1y7XRR6LHZIj6yLYnUi/mm2KXKBg==", "dev": true, "dependencies": {"@jest/transform": "^29.7.0", "@types/babel__core": "^7.1.14", "babel-plugin-istanbul": "^6.1.1", "babel-preset-jest": "^29.6.3", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.8.0"}}, "node_modules/babel-plugin-istanbul": {"version": "6.1.1", "resolved": "https://registry.npmjs.org/babel-plugin-istanbul/-/babel-plugin-istanbul-6.1.1.tgz", "integrity": "sha512-Y1IQok9821cC9onCx5otgFfRm7Lm+I+wwxOx738M/WLPZ9Q42m4IG5W0FNX8WLL2gYMZo3JkuXIH2DOpWM+qwA==", "dev": true, "dependencies": {"@babel/helper-plugin-utils": "^7.0.0", "@istanbuljs/load-nyc-config": "^1.0.0", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-instrument": "^5.0.4", "test-exclude": "^6.0.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-istanbul/node_modules/istanbul-lib-instrument": {"version": "5.2.1", "resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-5.2.1.tgz", "integrity": "sha512-pzqtp31nLv/XFOzXGuvhCb8qhjmTVo5vjVk19XE4CRlSWz0KoeJ3bw9XsA7nOp9YBf4qHjwBxkDzKcME/J29Yg==", "dev": true, "dependencies": {"@babel/core": "^7.12.3", "@babel/parser": "^7.14.7", "@istanbuljs/schema": "^0.1.2", "istanbul-lib-coverage": "^3.2.0", "semver": "^6.3.0"}, "engines": {"node": ">=8"}}, "node_modules/babel-plugin-jest-hoist": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/babel-plugin-jest-hoist/-/babel-plugin-jest-hoist-29.6.3.tgz", "integrity": "sha512-ESAc/RJvGTFEzRwOTT4+lNDk/GNHMkKbNzsvT0qKRfDyyYTskxB5rnU2njIDYVxXCBHHEI1c0YwHob3WaYujOg==", "dev": true, "dependencies": {"@babel/template": "^7.3.3", "@babel/types": "^7.3.3", "@types/babel__core": "^7.1.14", "@types/babel__traverse": "^7.0.6"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/babel-preset-current-node-syntax": {"version": "1.1.0", "resolved": "https://registry.npmjs.org/babel-preset-current-node-syntax/-/babel-preset-current-node-syntax-1.1.0.tgz", "integrity": "sha512-ldYss8SbBlWva1bs28q78Ju5Zq1F+8BrqBZZ0VFhLBvhh6lCpC2o3gDJi/5DRLs9FgYZCnmPYIVFU4lRXCkyUw==", "dev": true, "dependencies": {"@babel/plugin-syntax-async-generators": "^7.8.4", "@babel/plugin-syntax-bigint": "^7.8.3", "@babel/plugin-syntax-class-properties": "^7.12.13", "@babel/plugin-syntax-class-static-block": "^7.14.5", "@babel/plugin-syntax-import-attributes": "^7.24.7", "@babel/plugin-syntax-import-meta": "^7.10.4", "@babel/plugin-syntax-json-strings": "^7.8.3", "@babel/plugin-syntax-logical-assignment-operators": "^7.10.4", "@babel/plugin-syntax-nullish-coalescing-operator": "^7.8.3", "@babel/plugin-syntax-numeric-separator": "^7.10.4", "@babel/plugin-syntax-object-rest-spread": "^7.8.3", "@babel/plugin-syntax-optional-catch-binding": "^7.8.3", "@babel/plugin-syntax-optional-chaining": "^7.8.3", "@babel/plugin-syntax-private-property-in-object": "^7.14.5", "@babel/plugin-syntax-top-level-await": "^7.14.5"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/babel-preset-jest": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/babel-preset-jest/-/babel-preset-jest-29.6.3.tgz", "integrity": "sha512-0B3bhxR6snWXJZtR/RliHTDPRgn1sNHOR0yVtq/IiQFyuOVjFS+wuio/R4gSNkyYmKmJB4wGZv2NZanmKmTnNA==", "dev": true, "dependencies": {"babel-plugin-jest-hoist": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@babel/core": "^7.0.0"}}, "node_modules/balanced-match": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/balanced-match/-/balanced-match-1.0.2.tgz", "integrity": "sha512-3oSeUO0TMV67hN1AmbXsK4yaqU7tjiHlbxRDZOpH0KW9+CeX4bRAaX0Anxt0tx2MrpRpWwQaPwIlISEJhYU5Pw=="}, "node_modules/brace-expansion": {"version": "1.1.12", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-1.1.12.tgz", "integrity": "sha512-9T9UjW3r0UW5c1Q7GTwllptXwhvYmEzFhzMfZ9H7FQWt+uZePjZPjBP/W1ZEyZ1twGWom5/56TF4lPcqjnDHcg==", "dependencies": {"balanced-match": "^1.0.0", "concat-map": "0.0.1"}}, "node_modules/braces": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/braces/-/braces-3.0.3.tgz", "integrity": "sha512-yQbXgO/OSZVD2IsiLlro+7Hf6Q18EJrKSEsdoMzKePKXct3gvD8oLcOQdIzGupr5Fj+EDe8gO/lxc1BzfMpxvA==", "dev": true, "dependencies": {"fill-range": "^7.1.1"}, "engines": {"node": ">=8"}}, "node_modules/browserslist": {"version": "4.25.1", "resolved": "https://registry.npmjs.org/browserslist/-/browserslist-4.25.1.tgz", "integrity": "sha512-KGj0KoOMXLpSNkkEI6Z6mShmQy0bc1I+T7K9N81k4WWMrfz+6fQ6es80B/YLAeRoKvjYE1YSHHOW1qe9xIVzHw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"caniuse-lite": "^1.0.30001726", "electron-to-chromium": "^1.5.173", "node-releases": "^2.0.19", "update-browserslist-db": "^1.1.3"}, "bin": {"browserslist": "cli.js"}, "engines": {"node": "^6 || ^7 || ^8 || ^9 || ^10 || ^11 || ^12 || >=13.7"}}, "node_modules/bs-logger": {"version": "0.2.6", "resolved": "https://registry.npmjs.org/bs-logger/-/bs-logger-0.2.6.tgz", "integrity": "sha512-pd8DCoxmbgc7hyPKOvxtqNcjYoOsABPQdcCUjGp3d42VR2CX1ORhk2A87oqqu5R1kk+76nsxZupkmyd+MVtCog==", "dev": true, "dependencies": {"fast-json-stable-stringify": "2.x"}, "engines": {"node": ">= 6"}}, "node_modules/bser": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/bser/-/bser-2.1.1.tgz", "integrity": "sha512-gQxTNE/GAfIIrmHLUE3oJyp5FO6HRBfhjnw4/wMmA63ZGDJnWBmgY/lyQBpnDUkGmAhbSe39tx2d/iTOAfglwQ==", "dev": true, "dependencies": {"node-int64": "^0.4.0"}}, "node_modules/buffer-from": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/buffer-from/-/buffer-from-1.1.2.tgz", "integrity": "sha512-E+XQCRwSbaaiChtv6k6Dwgc+bx+Bs6vuKJHHl5kox/BaKbhiXzqQOwK4cO22yElGp2OCmjwVhT3HmxgyPGnJfQ==", "dev": true}, "node_modules/callsites": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/callsites/-/callsites-3.1.0.tgz", "integrity": "sha512-P8BjAsXvZS+VIDUI11hHCQEv74YT67YUi5JJFNWIqL235sBmjX4+qx9Muvls5ivyNENctx46xQLQ3aTuE7ssaQ==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/camelcase": {"version": "5.3.1", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-5.3.1.tgz", "integrity": "sha512-L28STB170nwWS63UjtlEOE3dldQApaJXZkOI1uMFfzf3rRuPegHaHesyee+YxQ+W6SvRDQV6UrdOdRiR153wJg==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/caniuse-lite": {"version": "1.0.30001726", "resolved": "https://registry.npmjs.org/caniuse-lite/-/caniuse-lite-1.0.30001726.tgz", "integrity": "sha512-VQAUIUzBiZ/UnlM28fSp2CRF3ivUn1BWEvxMcVTNwpw91Py1pGbPIyIKtd+tzct9C3ouceCVdGAXxZOpZAsgdw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/caniuse-lite"}, {"type": "github", "url": "https://github.com/sponsors/ai"}]}, "node_modules/chalk": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/chalk/-/chalk-4.1.2.tgz", "integrity": "sha512-oKnbhFyRIXpUuez8iBMmyEa4nbj4IOQyuhc/wy9kY7/WVPcwIO9VA668Pu8RkO7+0G76SLROeyw9CpQ061i4mA==", "dev": true, "dependencies": {"ansi-styles": "^4.1.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/chalk?sponsor=1"}}, "node_modules/char-regex": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/char-regex/-/char-regex-1.0.2.tgz", "integrity": "sha512-kWWXztvZ5SBQV+eRgKFeh8q5sLuZY2+8WUIzlxWVTg+oGwY14qylx1KbKzHd8P6ZYkAg0xyIDU9JMHhyJMZ1jw==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/ci-info": {"version": "3.9.0", "resolved": "https://registry.npmjs.org/ci-info/-/ci-info-3.9.0.tgz", "integrity": "sha512-NIxF55hv4nSqQswkAeiOi1r83xy8JldOFDTWiug55KBu9Jnblncd2U6ViHmYgHf01TPZS77NJBhBMKdWj9HQMQ==", "dev": true, "funding": [{"type": "github", "url": "https://github.com/sponsors/sibir<PERSON>-s"}], "engines": {"node": ">=8"}}, "node_modules/cjs-module-lexer": {"version": "1.4.3", "resolved": "https://registry.npmjs.org/cjs-module-lexer/-/cjs-module-lexer-1.4.3.tgz", "integrity": "sha512-9z8TZaGM1pfswYeXrUpzPrkx8UnWYdhJclsiYMm6x/w5+nN+8Tf/LnAgfLGQCm59qAOxU8WwHEq2vNwF6i4j+Q==", "dev": true}, "node_modules/cliui": {"version": "8.0.1", "resolved": "https://registry.npmjs.org/cliui/-/cliui-8.0.1.tgz", "integrity": "sha512-BSeNnyus75C4//NQ9gQt1/csTXyo/8Sb+afLAkzAptFuMsod9HFokGNudZpi/oQV73hnVK+sR+5PVRMd+Dr7YQ==", "dev": true, "dependencies": {"string-width": "^4.2.0", "strip-ansi": "^6.0.1", "wrap-ansi": "^7.0.0"}, "engines": {"node": ">=12"}}, "node_modules/co": {"version": "4.6.0", "resolved": "https://registry.npmjs.org/co/-/co-4.6.0.tgz", "integrity": "sha512-QVb0dM5HvG+uaxitm8wONl7jltx8dqhfU33DcqtOZcLSVIKSDDLDi7+0LbAKiyI8hD9u42m2YxXSkMGWThaecQ==", "dev": true, "engines": {"iojs": ">= 1.0.0", "node": ">= 0.12.0"}}, "node_modules/collect-v8-coverage": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/collect-v8-coverage/-/collect-v8-coverage-1.0.2.tgz", "integrity": "sha512-lHl4d5/ONEbLlJvaJNtsF/Lz+WvB07u2ycqTYbdrq7UypDXailES4valYb2eWiJFxZlVmpGekfqoxQhzyFdT4Q==", "dev": true}, "node_modules/color-convert": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/color-convert/-/color-convert-2.0.1.tgz", "integrity": "sha512-RRECPsj7iu/xb5oKYcsFHSppFNnsj/52OVTRKb4zP5onXwVF3zVmmToNcOfGC+CRDpfK/U584fMg38ZHCaElKQ==", "dev": true, "dependencies": {"color-name": "~1.1.4"}, "engines": {"node": ">=7.0.0"}}, "node_modules/color-name": {"version": "1.1.4", "resolved": "https://registry.npmjs.org/color-name/-/color-name-1.1.4.tgz", "integrity": "sha512-dOy+3AuW3a2wNbZHIuMZpTcgjGuLU/uBL/ubcZF9OXbDo8ff4O8yVp5Bf0efS8uEoYo5q4Fx7dY9OgQGXgAsQA==", "dev": true}, "node_modules/concat-map": {"version": "0.0.1", "resolved": "https://registry.npmjs.org/concat-map/-/concat-map-0.0.1.tgz", "integrity": "sha512-/Srv4dswyQNBfohGpz9o6Yb3Gz3SrUDqBH5rTuhGR7ahtlbYKnVxw2bCFMRljaA7EXHaXZ8wsHdodFvbkhKmqg=="}, "node_modules/constructs": {"version": "10.4.2", "resolved": "https://registry.npmjs.org/constructs/-/constructs-10.4.2.tgz", "integrity": "sha512-wsNxBlAott2qg8Zv87q3eYZYgheb9lchtBfjHzzLHtXbttwSrHPs1NNQbBrmbb1YZvYg2+Vh0Dor76w4mFxJkA=="}, "node_modules/convert-source-map": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/convert-source-map/-/convert-source-map-2.0.0.tgz", "integrity": "sha512-Kvp459HrV2FEJ1CAsi1Ku+MY3kasH19TFykTz2xWmMeq6bk2NU3XXvfJ+Q61m0xktWwt+1HSYf3JZsTms3aRJg==", "dev": true}, "node_modules/create-jest": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/create-jest/-/create-jest-29.7.0.tgz", "integrity": "sha512-Adz2bdH0Vq3F53KEMJOoftQFutWCukm6J24wbPWRO4k1kMY7gS7ds/uoJkNuV8wDCtWWnuwGcJwpWcih+zEW1Q==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "exit": "^0.1.2", "graceful-fs": "^4.2.9", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "prompts": "^2.0.1"}, "bin": {"create-jest": "bin/create-jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/create-require": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/create-require/-/create-require-1.1.1.tgz", "integrity": "sha512-dcKFX3jn0MpIaXjisoRvexIJVEKzaq7z2rZKxf+MSr9TkdmHmsU4m2lcLojrj/FHl8mk5VxMmYA+ftRkP/3oKQ==", "dev": true}, "node_modules/cross-spawn": {"version": "7.0.6", "resolved": "https://registry.npmjs.org/cross-spawn/-/cross-spawn-7.0.6.tgz", "integrity": "sha512-uV2QOWP2nWzsy2aMp8aRibhi9dlzF5Hgh5SHaB9OiTGEyDTiJJyx0uy51QXdyWbtAHNua4XJzUKca3OzKUd3vA==", "dev": true, "dependencies": {"path-key": "^3.1.0", "shebang-command": "^2.0.0", "which": "^2.0.1"}, "engines": {"node": ">= 8"}}, "node_modules/debug": {"version": "4.4.1", "resolved": "https://registry.npmjs.org/debug/-/debug-4.4.1.tgz", "integrity": "sha512-KcKCqiftBJcZr++7ykoDIEwSa3XWowTfNPo92BYxjXiyYEVrUQh2aLyhxBCwww+heortUFxEJYcRzosstTEBYQ==", "dev": true, "dependencies": {"ms": "^2.1.3"}, "engines": {"node": ">=6.0"}, "peerDependenciesMeta": {"supports-color": {"optional": true}}}, "node_modules/dedent": {"version": "1.6.0", "resolved": "https://registry.npmjs.org/dedent/-/dedent-1.6.0.tgz", "integrity": "sha512-F1Z+5UCFpmQUzJa11agbyPVMbpgT/qA3/SKyJ1jyBgm7dUcUEa8v9JwDkerSQXfakBwFljIxhOJqGkjUwZ9FSA==", "dev": true, "peerDependencies": {"babel-plugin-macros": "^3.1.0"}, "peerDependenciesMeta": {"babel-plugin-macros": {"optional": true}}}, "node_modules/deepmerge": {"version": "4.3.1", "resolved": "https://registry.npmjs.org/deepmerge/-/deepmerge-4.3.1.tgz", "integrity": "sha512-3sUqbMEc77XqpdNO7FRyRog+eW3ph+GYCbj+rK+uYyRMuwsVy0rMiVtPn+QJlKFvWP/1PYpapqYn0Me2knFn+A==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/detect-newline": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/detect-newline/-/detect-newline-3.1.0.tgz", "integrity": "sha512-TLz+x/vEXm/Y7P7wn1EJFNLxYpUD4TgMosxY6fAVJUnJMbupHBOncxyWUG9OpTaH9EBD7uFI5LfEgmMOc54DsA==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/diff": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/diff/-/diff-4.0.2.tgz", "integrity": "sha512-58lmxKSA4BNyLz+HHMUzlOEpg09FV+ev6ZMe3vJihgdxzgcwZ8VoEEPmALCZG9LmqfVoNMMKpttIYTVG6uDY7A==", "dev": true, "engines": {"node": ">=0.3.1"}}, "node_modules/diff-sequences": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/diff-sequences/-/diff-sequences-29.6.3.tgz", "integrity": "sha512-EjePK1srD3P08o2j4f0ExnylqRs5B9tJjcp9t1krH2qRi8CCdsYfwe9JgSLurFBWwq4uOlipzfk5fHNvwFKr8Q==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/ejs": {"version": "3.1.10", "resolved": "https://registry.npmjs.org/ejs/-/ejs-3.1.10.tgz", "integrity": "sha512-UeJmFfOrAQS8OJWPZ4qtgHyWExa088/MtK5UEyoJGFH67cDEXkZSviOiKRCZ4Xij0zxI3JECgYs3oKx+AizQBA==", "dev": true, "dependencies": {"jake": "^10.8.5"}, "bin": {"ejs": "bin/cli.js"}, "engines": {"node": ">=0.10.0"}}, "node_modules/electron-to-chromium": {"version": "1.5.177", "resolved": "https://registry.npmjs.org/electron-to-chromium/-/electron-to-chromium-1.5.177.tgz", "integrity": "sha512-7EH2G59nLsEMj97fpDuvVcYi6lwTcM1xuWw3PssD8xzboAW7zj7iB3COEEEATUfjLHrs5uKBLQT03V/8URx06g==", "dev": true}, "node_modules/emittery": {"version": "0.13.1", "resolved": "https://registry.npmjs.org/emittery/-/emittery-0.13.1.tgz", "integrity": "sha512-DeWwawk6r5yR9jFgnDKYt4sLS0LmHJJi3ZOnb5/JdbYwj3nW+FxQnHIjhBKz8YLC7oRNPVM9NQ47I3CVx34eqQ==", "dev": true, "engines": {"node": ">=12"}, "funding": {"url": "https://github.com/sindresorhus/emittery?sponsor=1"}}, "node_modules/emoji-regex": {"version": "8.0.0", "resolved": "https://registry.npmjs.org/emoji-regex/-/emoji-regex-8.0.0.tgz", "integrity": "sha512-MSjYzcWNOA0ewAHpz0MxpYFvwg6yjy1NG3xteoqz644VCo/RPgnr1/GGt+ic3iJTzQ8Eu3TdM14SawnVUmGE6A==", "dev": true}, "node_modules/error-ex": {"version": "1.3.2", "resolved": "https://registry.npmjs.org/error-ex/-/error-ex-1.3.2.tgz", "integrity": "sha512-7dFHNmqeFSEt2ZBsCriorKnn3Z2pj+fd9kmI6QoWw4//DL+icEBfc0U7qJCisqrTsKTjw4fNFy2pW9OqStD84g==", "dev": true, "dependencies": {"is-arrayish": "^0.2.1"}}, "node_modules/escalade": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/escalade/-/escalade-3.2.0.tgz", "integrity": "sha512-WUj2qlxaQtO4g6Pq5c29GTcWGDyd8itL8zTlipgECz3JesAiiOKotd8JU6otB3PACgG6xkJUyVhboMS+bje/jA==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/escape-string-regexp": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/escape-string-regexp/-/escape-string-regexp-2.0.0.tgz", "integrity": "sha512-UpzcLCXolUWcNu5HtVMHYdXJjArjsF9C0aNnquZYY4uW/Vu0miy5YoWvbV345HauVvcAUnpRuhMMcqTcGOY2+w==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/esprima": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/esprima/-/esprima-4.0.1.tgz", "integrity": "sha512-eGuFFw7Upda+g4p+QHvnW0RyTX/SVeJBDM/gCtMARO0cLuT2HcEKnTPvhjV6aGeqrCB/sbNop0Kszm0jsaWU4A==", "dev": true, "bin": {"esparse": "bin/esparse.js", "esvalidate": "bin/esvalidate.js"}, "engines": {"node": ">=4"}}, "node_modules/execa": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/execa/-/execa-5.1.1.tgz", "integrity": "sha512-8uSpZZocAZRBAPIEINJj3Lo9HyGitllczc27Eh5YYojjMFMn8yHMDMaUHE2Jqfq05D/wucwI4JGURyXt1vchyg==", "dev": true, "dependencies": {"cross-spawn": "^7.0.3", "get-stream": "^6.0.0", "human-signals": "^2.1.0", "is-stream": "^2.0.0", "merge-stream": "^2.0.0", "npm-run-path": "^4.0.1", "onetime": "^5.1.2", "signal-exit": "^3.0.3", "strip-final-newline": "^2.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sindresorhus/execa?sponsor=1"}}, "node_modules/exit": {"version": "0.1.2", "resolved": "https://registry.npmjs.org/exit/-/exit-0.1.2.tgz", "integrity": "sha512-Zk/eNKV2zbjpKzrsQ+n1G6poVbErQxJ0LBOJXaKZ1EViLzH+hrLu9cdXI4zw9dBQJslwBEpbQ2P1oS7nDxs6jQ==", "dev": true, "engines": {"node": ">= 0.8.0"}}, "node_modules/expect": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/expect/-/expect-29.7.0.tgz", "integrity": "sha512-2Zks0hf1VLFYI1kbh0I5jP3KHHyCHpkfyHBzsSXRFgl/Bg9mWYfMW8oD+PdMPlEwy5HNsR9JutYy6pMeOh61nw==", "dev": true, "dependencies": {"@jest/expect-utils": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/fast-json-stable-stringify": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/fast-json-stable-stringify/-/fast-json-stable-stringify-2.1.0.tgz", "integrity": "sha512-lhd/wF+Lk98HZoTCtlVraHtfh5XYijIjalXck7saUtuanSDyLMxnHhSXEDJqHxD7msR8D0uCmqlkwjCV8xvwHw==", "dev": true}, "node_modules/fb-watchman": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/fb-watchman/-/fb-watchman-2.0.2.tgz", "integrity": "sha512-p5161BqbuCaSnB8jIbzQHOlpgsPmK5rJVDfDKO91Axs5NC1uu3HRQm6wt9cd9/+GtQQIO53JdGXXoyDpTAsgYA==", "dev": true, "dependencies": {"bser": "2.1.1"}}, "node_modules/filelist": {"version": "1.0.4", "resolved": "https://registry.npmjs.org/filelist/-/filelist-1.0.4.tgz", "integrity": "sha512-w1cEuf3S+DrLCQL7ET6kz+gmlJdbq9J7yXCSjK/OZCPA+qEN1WyF4ZAf0YYJa4/shHJra2t/d/r8SV4Ji+x+8Q==", "dev": true, "dependencies": {"minimatch": "^5.0.1"}}, "node_modules/filelist/node_modules/brace-expansion": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/brace-expansion/-/brace-expansion-2.0.2.tgz", "integrity": "sha512-Jt0vHyM+jmUBqojB7E1NIYadt0vI0Qxjxd2TErW94wDz+E2LAm5vKMXXwg6ZZBTHPuUlDgQHKXvjGBdfcF1ZDQ==", "dev": true, "dependencies": {"balanced-match": "^1.0.0"}}, "node_modules/filelist/node_modules/minimatch": {"version": "5.1.6", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-5.1.6.tgz", "integrity": "sha512-lKwV/1brpG6mBUFHtb7NUmtABCb2WZZmm2wNiOA5hAb8VdCS4B3dtMWyvcoViccwAW/COERjXLt0zP1zXUN26g==", "dev": true, "dependencies": {"brace-expansion": "^2.0.1"}, "engines": {"node": ">=10"}}, "node_modules/fill-range": {"version": "7.1.1", "resolved": "https://registry.npmjs.org/fill-range/-/fill-range-7.1.1.tgz", "integrity": "sha512-YsGpe3WHLK8ZYi4tWDg2Jy3ebRz2rXowDxnld4bkQB00cc/1Zw9AWnC0i9ztDJitivtQvaI9KaLyKrc+hBW0yg==", "dev": true, "dependencies": {"to-regex-range": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/find-up": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/find-up/-/find-up-4.1.0.tgz", "integrity": "sha512-PpOwAdQ/YlXQ2vj8a3h8IipDuYRi3wceVQQGYWxNINccq40Anw7BlsEXCMbt1Zt+OLA6Fq9suIpIWD0OsnISlw==", "dev": true, "dependencies": {"locate-path": "^5.0.0", "path-exists": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/fs.realpath": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/fs.realpath/-/fs.realpath-1.0.0.tgz", "integrity": "sha512-OO0pH2lK6a0hZnAdau5ItzHPI6pUlvI7jMVnxUQRtw4owF2wk8lOSabtGDCTP4Ggrg2MbGnWO9X8K1t4+fGMDw==", "dev": true}, "node_modules/fsevents": {"version": "2.3.2", "resolved": "https://registry.npmjs.org/fsevents/-/fsevents-2.3.2.tgz", "integrity": "sha512-xiqMQR4xAeHTuB9uWm+fFRcIOgKBMiOBP+eXiyT7jsgVCq1bkVygt00oASowB7EdtpOHaaPgKt812P9ab+DDKA==", "dev": true, "hasInstallScript": true, "optional": true, "os": ["darwin"], "engines": {"node": "^8.16.0 || ^10.6.0 || >=11.0.0"}}, "node_modules/function-bind": {"version": "1.1.2", "resolved": "https://registry.npmjs.org/function-bind/-/function-bind-1.1.2.tgz", "integrity": "sha512-7XHNxH7qX9xG5mIwxkhumTox/MIRNcOgDrxWsMt2pAr23WHp6MrRlN7FBSFpCpr+oVO0F744iUgR82nJMfG2SA==", "dev": true, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/gensync": {"version": "1.0.0-beta.2", "resolved": "https://registry.npmjs.org/gensync/-/gensync-1.0.0-beta.2.tgz", "integrity": "sha512-3hN7NaskYvMDLQY55gnW3NQ+mesEAepTqlg+VEbj7zzqEMBVNhzcGYYeqFo/TlYz6eQiFcp1HcsCZO+nGgS8zg==", "dev": true, "engines": {"node": ">=6.9.0"}}, "node_modules/get-caller-file": {"version": "2.0.5", "resolved": "https://registry.npmjs.org/get-caller-file/-/get-caller-file-2.0.5.tgz", "integrity": "sha512-DyFP3BM/3YHTQOCUL/w0OZHR0lpKeGrxotcHWcqNEdnltqFwXVfhEBQ94eIo34AfQpo0rGki4cyIiftY06h2Fg==", "dev": true, "engines": {"node": "6.* || 8.* || >= 10.*"}}, "node_modules/get-package-type": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/get-package-type/-/get-package-type-0.1.0.tgz", "integrity": "sha512-pjzuKtY64GYfWizNAJ0fr9VqttZkNiK2iS430LtIHzjBEr6bX8Am2zm4sW4Ro5wjWW5cAlRL1qAMTcXbjNAO2Q==", "dev": true, "engines": {"node": ">=8.0.0"}}, "node_modules/get-stream": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/get-stream/-/get-stream-6.0.1.tgz", "integrity": "sha512-ts6Wi+2j3jQjqi70w5AlN8DFnkSwC+MqmxEzdEALB2qXZYV3X/b1CTfgPLGJNMeAWxdPfU8FO1ms3NUfaHCPYg==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/glob": {"version": "7.2.3", "resolved": "https://registry.npmjs.org/glob/-/glob-7.2.3.tgz", "integrity": "sha512-nFR0zLpU2YCaRxwoCJvL6UvCH2JFyFVIvwTLsIf21AuHlMskA1hhTdk+LlYJtOlYt9v6dvszD2BGRqBL+iQK9Q==", "deprecated": "Glob versions prior to v9 are no longer supported", "dev": true, "dependencies": {"fs.realpath": "^1.0.0", "inflight": "^1.0.4", "inherits": "2", "minimatch": "^3.1.1", "once": "^1.3.0", "path-is-absolute": "^1.0.0"}, "engines": {"node": "*"}, "funding": {"url": "https://github.com/sponsors/isaacs"}}, "node_modules/globals": {"version": "11.12.0", "resolved": "https://registry.npmjs.org/globals/-/globals-11.12.0.tgz", "integrity": "sha512-WOBp/EEGUiIsJSp7wcv/y6MO+lV9UoncWqxuFfm8eBwzWNgyfBd6Gz+IeKQ9jCmyhoH99g15M3T+QaVHFjizVA==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/graceful-fs": {"version": "4.2.11", "resolved": "https://registry.npmjs.org/graceful-fs/-/graceful-fs-4.2.11.tgz", "integrity": "sha512-RbJ5/jmFcNNCcDV5o9eTnBLJ/HszWV0P73bc+Ff4nS/rJj+YaS6IGyiOL0VoBYX+l1Wrl3k63h/KrH+nhJ0XvQ==", "dev": true}, "node_modules/has-flag": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/has-flag/-/has-flag-4.0.0.tgz", "integrity": "sha512-EykJT/Q1KjTWctppgIAgfSO0tKVuZUjhgMr17kqTumMl6Afv3EISleU7qZUzoXDFTAHTDC4NOoG/ZxU3EvlMPQ==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/hasown": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/hasown/-/hasown-2.0.2.tgz", "integrity": "sha512-0hJU9SCPvmMzIBdZFqNPXWa6dqh7WdH0cII9y+CyS8rG3nL48Bclra9HmKhVVUHyPWNH5Y7xDwAB7bfgSjkUMQ==", "dev": true, "dependencies": {"function-bind": "^1.1.2"}, "engines": {"node": ">= 0.4"}}, "node_modules/html-escaper": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/html-escaper/-/html-escaper-2.0.2.tgz", "integrity": "sha512-H2iMtd0I4Mt5eYiapRdIDjp+XzelXQ0tFE4JS7YFwFevXXMmOp9myNrUvCg0D6ws8iqkRPBfKHgbwig1SmlLfg==", "dev": true}, "node_modules/human-signals": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/human-signals/-/human-signals-2.1.0.tgz", "integrity": "sha512-B4FFZ6q/T2jhhksgkbEW3HBvWIfDW85snkQgawt07S7J5QXTk6BkNV+0yAeZrM5QpMAdYlocGoljn0sJ/WQkFw==", "dev": true, "engines": {"node": ">=10.17.0"}}, "node_modules/import-local": {"version": "3.2.0", "resolved": "https://registry.npmjs.org/import-local/-/import-local-3.2.0.tgz", "integrity": "sha512-2SPlun1JUPWoM6t3F0dw0FkCF/jWY8kttcY4f599GLTSjh2OCuuhdTkJQsEcZzBqbXZGKMK2OqW1oZsjtf/gQA==", "dev": true, "dependencies": {"pkg-dir": "^4.2.0", "resolve-cwd": "^3.0.0"}, "bin": {"import-local-fixture": "fixtures/cli.js"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/imurmurhash": {"version": "0.1.4", "resolved": "https://registry.npmjs.org/imurmurhash/-/imurmurhash-0.1.4.tgz", "integrity": "sha512-JmXMZ6wuvDmLiHEml9ykzqO6lwFbof0GG4IkcGaENdCRDDmMVnny7s5HsIgHCbaq0w2MyPhDqkhTUgS2LU2PHA==", "dev": true, "engines": {"node": ">=0.8.19"}}, "node_modules/inflight": {"version": "1.0.6", "resolved": "https://registry.npmjs.org/inflight/-/inflight-1.0.6.tgz", "integrity": "sha512-k92I/b08q4wvFscXCLvqfsHCrjrF7yiXsQuIVvVE7N82W3+aqpzuUdBbfhWcy/FZR3/4IgflMgKLOsvPDrGCJA==", "deprecated": "This module is not supported, and leaks memory. Do not use it. Check out lru-cache if you want a good and tested way to coalesce async requests by a key value, which is much more comprehensive and powerful.", "dev": true, "dependencies": {"once": "^1.3.0", "wrappy": "1"}}, "node_modules/inherits": {"version": "2.0.4", "resolved": "https://registry.npmjs.org/inherits/-/inherits-2.0.4.tgz", "integrity": "sha512-k/vGaX4/Yla3WzyMCvTQOXYeIHvqOKtnqBduzTHpzpQZzAskKMhZ2K+EnBiSM9zGSoIFeMpXKxa4dYeZIQqewQ==", "dev": true}, "node_modules/is-arrayish": {"version": "0.2.1", "resolved": "https://registry.npmjs.org/is-arrayish/-/is-arrayish-0.2.1.tgz", "integrity": "sha512-zz06S8t0ozoDXMG+ube26zeCTNXcKIPJZJi8hBrF4idCLms4CG9QtK7qBl1boi5ODzFpjswb5JPmHCbMpjaYzg==", "dev": true}, "node_modules/is-core-module": {"version": "2.16.1", "resolved": "https://registry.npmjs.org/is-core-module/-/is-core-module-2.16.1.tgz", "integrity": "sha512-UfoeMA6fIJ8wTYFEUjelnaGI67v6+N7qXJEvQuIGa99l4xsCruSYOVSQ0uPANn4dAzm8lkYPaKLrrijLq7x23w==", "dev": true, "dependencies": {"hasown": "^2.0.2"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/is-fullwidth-code-point": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/is-fullwidth-code-point/-/is-fullwidth-code-point-3.0.0.tgz", "integrity": "sha512-zymm5+u+sCsSWyD9qNaejV3DFvhCKclKdizYaJUuHA83RLjb7nSuGnddCHGv0hk+KY7BMAlsWeK4Ueg6EV6XQg==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/is-generator-fn": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/is-generator-fn/-/is-generator-fn-2.1.0.tgz", "integrity": "sha512-cTIB4yPYL/Grw0EaSzASzg6bBy9gqCofvWN8okThAYIxKJZC+udlRAmGbM0XLeniEJSs8uEgHPGuHSe1XsOLSQ==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/is-number": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/is-number/-/is-number-7.0.0.tgz", "integrity": "sha512-41Cifkg6e8TylSpdtTpeLVMqvSBEVzTttHvERD741+pnZ8ANv0004MRL43QKPDlK9cGvNp6NZWZUBlbGXYxxng==", "dev": true, "engines": {"node": ">=0.12.0"}}, "node_modules/is-stream": {"version": "2.0.1", "resolved": "https://registry.npmjs.org/is-stream/-/is-stream-2.0.1.tgz", "integrity": "sha512-hFoiJiTl63nn+kstHGBtewWSKnQLpyb155KHheA1l39uvtO9nWIop1p3udqPcUd/xbF1VLMO4n7OI6p7RbngDg==", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/isexe": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/isexe/-/isexe-2.0.0.tgz", "integrity": "sha512-RHxMLp9lnKHGHRng9QFhRCMbYAcVpn69smSGcq3f36xjgVVWThj4qqLbTLlq7Ssj8B+fIQ1EuCEGI2lKsyQeIw==", "dev": true}, "node_modules/istanbul-lib-coverage": {"version": "3.2.2", "resolved": "https://registry.npmjs.org/istanbul-lib-coverage/-/istanbul-lib-coverage-3.2.2.tgz", "integrity": "sha512-O8dpsF+r0WV/8MNRKfnmrtCWhuKjxrq2w+jpzBL5UZKTi2LeVWnWOmWRxFlesJONmc+wLAGvKQZEOanko0LFTg==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/istanbul-lib-instrument": {"version": "6.0.3", "resolved": "https://registry.npmjs.org/istanbul-lib-instrument/-/istanbul-lib-instrument-6.0.3.tgz", "integrity": "sha512-Vtgk7L/R2JHyyGW07spoFlB8/lpjiOLTjMdms6AFMraYt3BaJauod/NGrfnVG/y4Ix1JEuMRPDPEj2ua+zz1/Q==", "dev": true, "dependencies": {"@babel/core": "^7.23.9", "@babel/parser": "^7.23.9", "@istanbuljs/schema": "^0.1.3", "istanbul-lib-coverage": "^3.2.0", "semver": "^7.5.4"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-instrument/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-report": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/istanbul-lib-report/-/istanbul-lib-report-3.0.1.tgz", "integrity": "sha512-GCfE1mtsHGOELCU8e/Z7YWzpmybrx/+dSTfLrvY8qRmaY6zXTKWn6WQIjaAFw069icm6GVMNkgu0NzI4iPZUNw==", "dev": true, "dependencies": {"istanbul-lib-coverage": "^3.0.0", "make-dir": "^4.0.0", "supports-color": "^7.1.0"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-lib-source-maps": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/istanbul-lib-source-maps/-/istanbul-lib-source-maps-4.0.1.tgz", "integrity": "sha512-n3s8EwkdFIJCG3BPKBYvskgXGoy88ARzvegkitk60NxRdwltLOTaH7CUiMRXvwYorl0Q712iEjcWB+fK/MrWVw==", "dev": true, "dependencies": {"debug": "^4.1.1", "istanbul-lib-coverage": "^3.0.0", "source-map": "^0.6.1"}, "engines": {"node": ">=10"}}, "node_modules/istanbul-reports": {"version": "3.1.7", "resolved": "https://registry.npmjs.org/istanbul-reports/-/istanbul-reports-3.1.7.tgz", "integrity": "sha512-BewmUXImeuRk2YY0PVbxgKAysvhRPUQE0h5QRM++nVWyubKGV0l8qQ5op8+B2DOmwSe63Jivj0BjkPQVf8fP5g==", "dev": true, "dependencies": {"html-escaper": "^2.0.0", "istanbul-lib-report": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/jake": {"version": "10.9.2", "resolved": "https://registry.npmjs.org/jake/-/jake-10.9.2.tgz", "integrity": "sha512-2P4SQ0HrLQ+fw6llpLnOaGAvN2Zu6778SJMrCUwns4fOoG9ayrTiZk3VV8sCPkVZF8ab0zksVpS8FDY5pRCNBA==", "dev": true, "dependencies": {"async": "^3.2.3", "chalk": "^4.0.2", "filelist": "^1.0.4", "minimatch": "^3.1.2"}, "bin": {"jake": "bin/cli.js"}, "engines": {"node": ">=10"}}, "node_modules/jest": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest/-/jest-29.7.0.tgz", "integrity": "sha512-NIy3oAFp9shda19hy4HK0HRTWKtPJmGdnvywu01nOqNC2vZg+Z+fvJDxpMQA88eb2I9EcafcdjYgsDthnYTvGw==", "dev": true, "dependencies": {"@jest/core": "^29.7.0", "@jest/types": "^29.6.3", "import-local": "^3.0.2", "jest-cli": "^29.7.0"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-changed-files": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-changed-files/-/jest-changed-files-29.7.0.tgz", "integrity": "sha512-fEArFiwf1BpQ+4bXSprcDc3/x4HSzL4al2tozwVpDFpsxALjLYdyiIK4e5Vz66GQJIbXJ82+35PtysofptNX2w==", "dev": true, "dependencies": {"execa": "^5.0.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-circus": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-circus/-/jest-circus-29.7.0.tgz", "integrity": "sha512-3E1nCMgipcTkCocFwM90XXQab9bS+GMsjdpmPrlelaxwD93Ad8iVEjX/vvHPdLPnFf+L40u+5+iutRdA1N9myw==", "dev": true, "dependencies": {"@jest/environment": "^29.7.0", "@jest/expect": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "co": "^4.6.0", "dedent": "^1.0.0", "is-generator-fn": "^2.0.0", "jest-each": "^29.7.0", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-runtime": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "p-limit": "^3.1.0", "pretty-format": "^29.7.0", "pure-rand": "^6.0.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-cli": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-cli/-/jest-cli-29.7.0.tgz", "integrity": "sha512-OVVobw2IubN/GSYsxETi+gOe7Ka59EFMR/twOU3Jb2GnKKeMGJB5SGUUrEz3SFVmJASUdZUzy83sLNNQ2gZslg==", "dev": true, "dependencies": {"@jest/core": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "chalk": "^4.0.0", "create-jest": "^29.7.0", "exit": "^0.1.2", "import-local": "^3.0.2", "jest-config": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "yargs": "^17.3.1"}, "bin": {"jest": "bin/jest.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"node-notifier": "^8.0.1 || ^9.0.0 || ^10.0.0"}, "peerDependenciesMeta": {"node-notifier": {"optional": true}}}, "node_modules/jest-config": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-config/-/jest-config-29.7.0.tgz", "integrity": "sha512-uXbpfeQ7R6TZBqI3/TxCU4q4ttk3u0PJeC+E0zbfSoSjq6bJ7buBPxzQPL0ifrkY4DNu4JUdk0ImlBUYi840eQ==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@jest/test-sequencer": "^29.7.0", "@jest/types": "^29.6.3", "babel-jest": "^29.7.0", "chalk": "^4.0.0", "ci-info": "^3.2.0", "deepmerge": "^4.2.2", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-circus": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-get-type": "^29.6.3", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-runner": "^29.7.0", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "micromatch": "^4.0.4", "parse-json": "^5.2.0", "pretty-format": "^29.7.0", "slash": "^3.0.0", "strip-json-comments": "^3.1.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "peerDependencies": {"@types/node": "*", "ts-node": ">=9.0.0"}, "peerDependenciesMeta": {"@types/node": {"optional": true}, "ts-node": {"optional": true}}}, "node_modules/jest-diff": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-diff/-/jest-diff-29.7.0.tgz", "integrity": "sha512-LMIgiIrhigmPrs03JHpxUh2yISK3vLFPkAodPeo0+BuF7wA2FoQbkEg1u8gBYBThncu7e1oEDUfIXVuTqLRUjw==", "dev": true, "dependencies": {"chalk": "^4.0.0", "diff-sequences": "^29.6.3", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-docblock": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-docblock/-/jest-docblock-29.7.0.tgz", "integrity": "sha512-q617Auw3A612guyaFgsbFeYpNP5t2aoUNLwBUbc/0kD1R4t9ixDbyFTHd1nok4epoVFpr7PmeWHrhvuV3XaJ4g==", "dev": true, "dependencies": {"detect-newline": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-each": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-each/-/jest-each-29.7.0.tgz", "integrity": "sha512-gns+Er14+ZrEoC5fhOfYCY1LOHHr0TI+rQUHZS8Ttw2l7gl+80eHc/gFf2Ktkw0+SIACDTeWvpFcv3B04VembQ==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "jest-util": "^29.7.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-environment-node": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-environment-node/-/jest-environment-node-29.7.0.tgz", "integrity": "sha512-DOSwCRqXirTOyheM+4d5YZOrWcdu0LNZ87ewUoywbcb2XR4wKgqiG8vNeYwhjFMbEkfju7wx2GYH0P2gevGvFw==", "dev": true, "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "jest-mock": "^29.7.0", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-get-type": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/jest-get-type/-/jest-get-type-29.6.3.tgz", "integrity": "sha512-zrteXnqYxfQh7l5FHyL38jL39di8H8rHoecLH3JNxH3BwOrBsNeabdap5e0I23lD4HHI8W5VFBZqG4Eaq5LNcw==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-haste-map": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-haste-map/-/jest-haste-map-29.7.0.tgz", "integrity": "sha512-fP8u2pyfqx0K1rGn1R9pyE0/KTn+G7PxktWidOBTqFPLYX0b9ksaMFkhK5vrS3DVun09pckLdlx90QthlW7AmA==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "@types/graceful-fs": "^4.1.3", "@types/node": "*", "anymatch": "^3.0.3", "fb-watchman": "^2.0.0", "graceful-fs": "^4.2.9", "jest-regex-util": "^29.6.3", "jest-util": "^29.7.0", "jest-worker": "^29.7.0", "micromatch": "^4.0.4", "walker": "^1.0.8"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}, "optionalDependencies": {"fsevents": "^2.3.2"}}, "node_modules/jest-leak-detector": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-leak-detector/-/jest-leak-detector-29.7.0.tgz", "integrity": "sha512-kYA8IJcSYtST2BY9I+SMC32nDpBT3J2NvWJx8+JCuCdl/CR1I4EKUJROiP8XtCcxqgTTBGJNdbB1A8XRKbTetw==", "dev": true, "dependencies": {"jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-matcher-utils": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-matcher-utils/-/jest-matcher-utils-29.7.0.tgz", "integrity": "sha512-sBkD+Xi9DtcChsI3L3u0+N0opgPYnCRPtGcQYrgXmR+hmt/fYfWAL0xRXYU8eWOdfuLgBe0YCW3AFtnRLagq/g==", "dev": true, "dependencies": {"chalk": "^4.0.0", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-message-util": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-message-util/-/jest-message-util-29.7.0.tgz", "integrity": "sha512-GBEV4GRADeP+qtB2+6u61stea8mGcOT4mCtrYISZwfu9/ISHFJ/5zOMXYbpBE9RsS5+Gb63DW4FgmnKJ79Kf6w==", "dev": true, "dependencies": {"@babel/code-frame": "^7.12.13", "@jest/types": "^29.6.3", "@types/stack-utils": "^2.0.0", "chalk": "^4.0.0", "graceful-fs": "^4.2.9", "micromatch": "^4.0.4", "pretty-format": "^29.7.0", "slash": "^3.0.0", "stack-utils": "^2.0.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-mock": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-mock/-/jest-mock-29.7.0.tgz", "integrity": "sha512-ITOMZn+UkYS4ZFh83xYAOzWStloNzJFO2s8DWrE4lhtGD+AorgnbkiKERe4wQVBydIGPx059g6riW5Btp6Llnw==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "jest-util": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-pnp-resolver": {"version": "1.2.3", "resolved": "https://registry.npmjs.org/jest-pnp-resolver/-/jest-pnp-resolver-1.2.3.tgz", "integrity": "sha512-+3NpwQEnRoIBtx4fyhblQDPgJI0H1IEIkX7ShLUjPGA7TtUTvI1oiKi3SR4oBR0hQhQR80l4WAe5RrXBwWMA8w==", "dev": true, "engines": {"node": ">=6"}, "peerDependencies": {"jest-resolve": "*"}, "peerDependenciesMeta": {"jest-resolve": {"optional": true}}}, "node_modules/jest-regex-util": {"version": "29.6.3", "resolved": "https://registry.npmjs.org/jest-regex-util/-/jest-regex-util-29.6.3.tgz", "integrity": "sha512-KJJBsRCyyLNWCNBOvZyRDnAIfUiRJ8v+hOBQYGn8gDyF3UegwiP4gwRR3/SDa42g1YbVycTidUF3rKjyLFDWbg==", "dev": true, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-resolve/-/jest-resolve-29.7.0.tgz", "integrity": "sha512-IOVhZSrg+UvVAshDSDtHyFCCBUl/Q3AAJv8iZ6ZjnZ74xzvwuzLXid9IIIPgTnY62SJjfuupMKZsZQRsCvxEgA==", "dev": true, "dependencies": {"chalk": "^4.0.0", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-pnp-resolver": "^1.2.2", "jest-util": "^29.7.0", "jest-validate": "^29.7.0", "resolve": "^1.20.0", "resolve.exports": "^2.0.0", "slash": "^3.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-resolve-dependencies": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-resolve-dependencies/-/jest-resolve-dependencies-29.7.0.tgz", "integrity": "sha512-un0zD/6qxJ+S0et7WxeI3H5XSe9lTBBR7bOHCHXkKR6luG5mwDDlIzVQ0V5cZCuoTgEdcdwzTghYkTWfubi+nA==", "dev": true, "dependencies": {"jest-regex-util": "^29.6.3", "jest-snapshot": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runner": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-runner/-/jest-runner-29.7.0.tgz", "integrity": "sha512-fsc4N6cPCAahybGBfTRcq5wFR6fpLznMg47sY5aDpsoejOcVYFb07AHuSnR0liMcPTgBsA3ZJL6kFOjPdoNipQ==", "dev": true, "dependencies": {"@jest/console": "^29.7.0", "@jest/environment": "^29.7.0", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "emittery": "^0.13.1", "graceful-fs": "^4.2.9", "jest-docblock": "^29.7.0", "jest-environment-node": "^29.7.0", "jest-haste-map": "^29.7.0", "jest-leak-detector": "^29.7.0", "jest-message-util": "^29.7.0", "jest-resolve": "^29.7.0", "jest-runtime": "^29.7.0", "jest-util": "^29.7.0", "jest-watcher": "^29.7.0", "jest-worker": "^29.7.0", "p-limit": "^3.1.0", "source-map-support": "0.5.13"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-runtime": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-runtime/-/jest-runtime-29.7.0.tgz", "integrity": "sha512-gUnLjgwdGqW7B4LvOIkbKs9WGbn+QLqRQQ9juC6HndeDiezIwhDP+mhMwHWCEcfQ5RUXa6OPnFF8BJh5xegwwQ==", "dev": true, "dependencies": {"@jest/environment": "^29.7.0", "@jest/fake-timers": "^29.7.0", "@jest/globals": "^29.7.0", "@jest/source-map": "^29.6.3", "@jest/test-result": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "cjs-module-lexer": "^1.0.0", "collect-v8-coverage": "^1.0.0", "glob": "^7.1.3", "graceful-fs": "^4.2.9", "jest-haste-map": "^29.7.0", "jest-message-util": "^29.7.0", "jest-mock": "^29.7.0", "jest-regex-util": "^29.6.3", "jest-resolve": "^29.7.0", "jest-snapshot": "^29.7.0", "jest-util": "^29.7.0", "slash": "^3.0.0", "strip-bom": "^4.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-snapshot/-/jest-snapshot-29.7.0.tgz", "integrity": "sha512-Rm0BMWtxBcioHr1/OX5YCP8Uov4riHvKPknOGs804Zg9JGZgmIBkbtlxJC/7Z4msKYVbIJtfU+tKb8xlYNfdkw==", "dev": true, "dependencies": {"@babel/core": "^7.11.6", "@babel/generator": "^7.7.2", "@babel/plugin-syntax-jsx": "^7.7.2", "@babel/plugin-syntax-typescript": "^7.7.2", "@babel/types": "^7.3.3", "@jest/expect-utils": "^29.7.0", "@jest/transform": "^29.7.0", "@jest/types": "^29.6.3", "babel-preset-current-node-syntax": "^1.0.0", "chalk": "^4.0.0", "expect": "^29.7.0", "graceful-fs": "^4.2.9", "jest-diff": "^29.7.0", "jest-get-type": "^29.6.3", "jest-matcher-utils": "^29.7.0", "jest-message-util": "^29.7.0", "jest-util": "^29.7.0", "natural-compare": "^1.4.0", "pretty-format": "^29.7.0", "semver": "^7.5.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-snapshot/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/jest-util": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-util/-/jest-util-29.7.0.tgz", "integrity": "sha512-z6<PERSON>bKajIpqGKU56y5KBUgy1dt1ihhQJgWzUlZHArA/+X2ad7Cb5iF+AK1EWVL/Bo7Rz9uurpqw6SiBCefUbCGA==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "@types/node": "*", "chalk": "^4.0.0", "ci-info": "^3.2.0", "graceful-fs": "^4.2.9", "picomatch": "^2.2.3"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-validate/-/jest-validate-29.7.0.tgz", "integrity": "sha512-ZB7wHqaRGVw/9hST/OuFUReG7M8vKeq0/J2egIGLdvjHCmYqGARhzXmtgi+gVeZ5uXFF219aOc3Ls2yLg27tkw==", "dev": true, "dependencies": {"@jest/types": "^29.6.3", "camelcase": "^6.2.0", "chalk": "^4.0.0", "jest-get-type": "^29.6.3", "leven": "^3.1.0", "pretty-format": "^29.7.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-validate/node_modules/camelcase": {"version": "6.3.0", "resolved": "https://registry.npmjs.org/camelcase/-/camelcase-6.3.0.tgz", "integrity": "sha512-Gmy6FhYlCY7uOElZUSbxo2UCDH8owEk996gkbrpsgGtrJLM3J7jGxl9Ic7Qwwj4ivOE5AWZWRMecDdF7hqGjFA==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/jest-watcher": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-watcher/-/jest-watcher-29.7.0.tgz", "integrity": "sha512-49Fg7WXkU3Vl2h6LbLtMQ/HyB6rXSIX7SqvBLQmssRBGN9I0PNvPmAmCWSOY6SOvrjhI/F7/bGAv9RtnsPA03g==", "dev": true, "dependencies": {"@jest/test-result": "^29.7.0", "@jest/types": "^29.6.3", "@types/node": "*", "ansi-escapes": "^4.2.1", "chalk": "^4.0.0", "emittery": "^0.13.1", "jest-util": "^29.7.0", "string-length": "^4.0.1"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/jest-worker/-/jest-worker-29.7.0.tgz", "integrity": "sha512-eIz2msL/EzL9UFTFFx7jBTkeZfku0yUAyZZZmJ93H2TYEiroIx2PQjEXcwYtYl8zXCxb+PAmA2hLIt/6ZEkPHw==", "dev": true, "dependencies": {"@types/node": "*", "jest-util": "^29.7.0", "merge-stream": "^2.0.0", "supports-color": "^8.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/jest-worker/node_modules/supports-color": {"version": "8.1.1", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-8.1.1.tgz", "integrity": "sha512-MpUEN2OodtUzxvKQl72cUF7RQ5EiHsGvSsVG0ia9c5RbWGL2CI4C7EpPS8UTBIplnlzZiNuV56w+FuNxy3ty2Q==", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/supports-color?sponsor=1"}}, "node_modules/js-tokens": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/js-tokens/-/js-tokens-4.0.0.tgz", "integrity": "sha512-RdJUflcE3cUzKiMqQgsCu06FPu9UdIJO0beYbPhHN4k6apgJtifcoCtT9bcxOpYBtpD2kCM6Sbzg4CausW/PKQ==", "dev": true}, "node_modules/js-yaml": {"version": "3.14.1", "resolved": "https://registry.npmjs.org/js-yaml/-/js-yaml-3.14.1.tgz", "integrity": "sha512-okMH7OXXJ7YrN9Ok3/SXrnu4iX9yOk+25nqX4imS2npuvTYDmo/QEZoqwZkYaIDk3jVvBOTOIEgEhaLOynBS9g==", "dev": true, "dependencies": {"argparse": "^1.0.7", "esprima": "^4.0.0"}, "bin": {"js-yaml": "bin/js-yaml.js"}}, "node_modules/jsesc": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/jsesc/-/jsesc-3.1.0.tgz", "integrity": "sha512-/sM3dO2FOzXjKQhJuo0Q173wf2KOo8t4I8vHy6lF9poUp7bKT0/NHE8fPX23PwfhnykfqnC2xRxOnVw5XuGIaA==", "dev": true, "bin": {"jsesc": "bin/jsesc"}, "engines": {"node": ">=6"}}, "node_modules/json-parse-even-better-errors": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/json-parse-even-better-errors/-/json-parse-even-better-errors-2.3.1.tgz", "integrity": "sha512-xyFwyhro/JEof6Ghe2iz2NcXoj2sloNsWr/XsERDK/oiPCfaNhl5ONfp+jQdAZRQQ0IJWNzH9zIZF7li91kh2w==", "dev": true}, "node_modules/json5": {"version": "2.2.3", "resolved": "https://registry.npmjs.org/json5/-/json5-2.2.3.tgz", "integrity": "sha512-<PERSON>m<PERSON>e7eyHYH14cLdVPoyg+GOH3rYX++KpzrylJwSW98t3Nk+U8XOl8FWKOgwtzdb8lXGf6zYwDUzeHMWfxasyg==", "dev": true, "bin": {"json5": "lib/cli.js"}, "engines": {"node": ">=6"}}, "node_modules/kleur": {"version": "3.0.3", "resolved": "https://registry.npmjs.org/kleur/-/kleur-3.0.3.tgz", "integrity": "sha512-eTIzlVOSUR+JxdDFepEYcBMtZ9Qqdef+rnzWdRZuMbOywu5tO2w2N7rqjoANZ5k9vywhL6Br1VRjUIgTQx4E8w==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/leven": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/leven/-/leven-3.1.0.tgz", "integrity": "sha512-qsda+H8jTaUaN/x5vzW2rzc+8Rw4TAQ/4KjB46IwK5VH+IlVeeeje/EoZRpiXvIqjFgK84QffqPztGI3VBLG1A==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/lines-and-columns": {"version": "1.2.4", "resolved": "https://registry.npmjs.org/lines-and-columns/-/lines-and-columns-1.2.4.tgz", "integrity": "sha512-7ylylesZQ/PV29jhEDl3Ufjo6ZX7gCqJr5F7PKrqc93v7fzSymt1BpwEU8nAUXs8qzzvqhbjhK5QZg6Mt/HkBg==", "dev": true}, "node_modules/locate-path": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/locate-path/-/locate-path-5.0.0.tgz", "integrity": "sha512-t7hw9pI+WvuwNJXwk5zVHpyhIqzg2qTlklJOf0mVxGSbe3Fp2VieZcduNYjaLDoy6p9uGpQEGWG87WpMKlNq8g==", "dev": true, "dependencies": {"p-locate": "^4.1.0"}, "engines": {"node": ">=8"}}, "node_modules/lodash.memoize": {"version": "4.1.2", "resolved": "https://registry.npmjs.org/lodash.memoize/-/lodash.memoize-4.1.2.tgz", "integrity": "sha512-t7j+NzmgnQzTAYXcsHYLgimltOV1MXHtlOWf6GjL9Kj8GK5FInw5JotxvbOs+IvV1/Dzo04/fCGfLVs7aXb4Ag==", "dev": true}, "node_modules/lru-cache": {"version": "5.1.1", "resolved": "https://registry.npmjs.org/lru-cache/-/lru-cache-5.1.1.tgz", "integrity": "sha512-KpNARQA3Iwv+jTA0utUVVbrh+Jlrr1Fv0e56GGzAFOXN7dk/FviaDW8LHmK52DlcH4WP2n6gI8vN1aesBFgo9w==", "dev": true, "dependencies": {"yallist": "^3.0.2"}}, "node_modules/make-dir": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/make-dir/-/make-dir-4.0.0.tgz", "integrity": "sha512-hXdUTZYIVOt1Ex//jAQi+wTZZpUpwBj/0QsOzqegb3rGMMeJiSEu5xLHnYfBrRV4RH2+OCSOO95Is/7x1WJ4bw==", "dev": true, "dependencies": {"semver": "^7.5.3"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/make-dir/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/make-error": {"version": "1.3.6", "resolved": "https://registry.npmjs.org/make-error/-/make-error-1.3.6.tgz", "integrity": "sha512-s8UhlNe7vPKomQhC1qFelMokr/Sc3AgNbso3n74mVPA5LTZwkB9NlXf4XPamLxJE8h0gh73rM94xvwRT2CVInw==", "dev": true}, "node_modules/makeerror": {"version": "1.0.12", "resolved": "https://registry.npmjs.org/makeerror/-/makeerror-1.0.12.tgz", "integrity": "sha512-JmqCvUhmt43madlpFzG4BQzG2Z3m6tvQDNKdClZnO3VbIudJYmxsT0FNJMeiB2+JTSlTQTSbU8QdesVmwJcmLg==", "dev": true, "dependencies": {"tmpl": "1.0.5"}}, "node_modules/merge-stream": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/merge-stream/-/merge-stream-2.0.0.tgz", "integrity": "sha512-abv/qOcuPfk3URPfDzmZU1LKmuw8kT+0nIHvKrKgFrwifol/doWcdA4ZqsWQ8ENrFKkd67Mfpo/LovbIUsbt3w==", "dev": true}, "node_modules/micromatch": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/micromatch/-/micromatch-4.0.8.tgz", "integrity": "sha512-PXwfBhYu0hBCPw8Dn0E+WDYb7af3dSLVWKi3HGv84IdF4TyFoC0ysxFd0Goxw7nSv4T/PzEJQxsYsEiFCKo2BA==", "dev": true, "dependencies": {"braces": "^3.0.3", "picomatch": "^2.3.1"}, "engines": {"node": ">=8.6"}}, "node_modules/mimic-fn": {"version": "2.1.0", "resolved": "https://registry.npmjs.org/mimic-fn/-/mimic-fn-2.1.0.tgz", "integrity": "sha512-OqbOk5oEQeAZ8WXWydlu9HJjz9WVdEIvamMCcXmuqUYjTknH/sqsWvhQ3vgwKFRR1HpjvNBKQ37nbJgYzGqGcg==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/minimatch": {"version": "3.1.2", "resolved": "https://registry.npmjs.org/minimatch/-/minimatch-3.1.2.tgz", "integrity": "sha512-J7p63hRiAjw1NDEww1W7i37+ByIrOWO5XQQAzZ3VOcL0PNybwpfmV/N05zFAzwQ9USyEcX6t3UO+K5aqBQOIHw==", "dependencies": {"brace-expansion": "^1.1.7"}, "engines": {"node": "*"}}, "node_modules/ms": {"version": "2.1.3", "resolved": "https://registry.npmjs.org/ms/-/ms-2.1.3.tgz", "integrity": "sha512-6FlzubTLZG3J2a/NVCAleEhjzq5oxgHyaCU9yYXvcLsvoVaHJq/s5xXI6/XXP6tz7R9xAOtHnSO/tXtF3WRTlA==", "dev": true}, "node_modules/natural-compare": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/natural-compare/-/natural-compare-1.4.0.tgz", "integrity": "sha512-OWND8ei3VtNC9h7V60qff3SVobHr996CTwgxubgyQYEpg290h9J0buyECNNJexkFm5sOajh5G116RYA1c8ZMSw==", "dev": true}, "node_modules/node-int64": {"version": "0.4.0", "resolved": "https://registry.npmjs.org/node-int64/-/node-int64-0.4.0.tgz", "integrity": "sha512-O5lz91xSOeoXP6DulyHfllpq+Eg00MWitZIbtPfoSEvqIHdl5gfcY6hYzDWnj0qD5tz52PI08u9qUvSVeUBeHw==", "dev": true}, "node_modules/node-releases": {"version": "2.0.19", "resolved": "https://registry.npmjs.org/node-releases/-/node-releases-2.0.19.tgz", "integrity": "sha512-xxOWJsBKtzAq7DY0J+DTzuz58K8e7sJbdgwkbMWQe8UYB6ekmsQ45q0M/tJDsGaZmbC+l7n57UV8Hl5tHxO9uw==", "dev": true}, "node_modules/normalize-path": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/normalize-path/-/normalize-path-3.0.0.tgz", "integrity": "sha512-6eZs5Ls3WtCisHWp9S2GUy8dqkpGi4BVSz3GaqiE6ezub0512ESztXUwUB6C6IKbQkY2Pnb/mD4WYojCRwcwLA==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/npm-run-path": {"version": "4.0.1", "resolved": "https://registry.npmjs.org/npm-run-path/-/npm-run-path-4.0.1.tgz", "integrity": "sha512-S48WzZW777zhNIrn7gxOlISNAqi9ZC/uQFnRdbeIHhZhCA6UqpkOT8T1G7BvfdgP4Er8gF4sUbaS0i7QvIfCWw==", "dev": true, "dependencies": {"path-key": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/once": {"version": "1.4.0", "resolved": "https://registry.npmjs.org/once/-/once-1.4.0.tgz", "integrity": "sha512-lNaJgI+2Q5URQBkccEKHTQOPaXdUxnZZElQTZY0MFUAuaEqe1E+Nyvgdz/aIyNi6Z9MzO5dv1H8n58/GELp3+w==", "dev": true, "dependencies": {"wrappy": "1"}}, "node_modules/onetime": {"version": "5.1.2", "resolved": "https://registry.npmjs.org/onetime/-/onetime-5.1.2.tgz", "integrity": "sha512-kbpaSSGJTWdAY5KPVeMOKXSrPtr8C8C7wodJbcsd51jRnmD+GZu8Y0VoU6Dm5Z4vWr0Ig/1NKuWRKf7j5aaYSg==", "dev": true, "dependencies": {"mimic-fn": "^2.1.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-limit": {"version": "3.1.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-3.1.0.tgz", "integrity": "sha512-TYOanM3wGwNGsZN2cVTYPArw454xnXj5qmWF1bEoAc4+cU/ol7GVh7odevjp1FNHduHc3KZMcFduxU5Xc6uJRQ==", "dev": true, "dependencies": {"yocto-queue": "^0.1.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-locate": {"version": "4.1.0", "resolved": "https://registry.npmjs.org/p-locate/-/p-locate-4.1.0.tgz", "integrity": "sha512-R79ZZ/0wAxKGu3oYMlz8jy/kbhsNrS7SKZ7PxEHBgJ5+F2mtFW2fK2cOtBh1cHYkQsbzFV7I+EoRKe6Yt0oK7A==", "dev": true, "dependencies": {"p-limit": "^2.2.0"}, "engines": {"node": ">=8"}}, "node_modules/p-locate/node_modules/p-limit": {"version": "2.3.0", "resolved": "https://registry.npmjs.org/p-limit/-/p-limit-2.3.0.tgz", "integrity": "sha512-//88mFWSJx8lxCzwdAABTJL2MyWB12+eIY7MDL2SqLmAkeKU9qxRvWuSyTjm3FUmpBEMuFfckAIqEaVGUDxb6w==", "dev": true, "dependencies": {"p-try": "^2.0.0"}, "engines": {"node": ">=6"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/p-try": {"version": "2.2.0", "resolved": "https://registry.npmjs.org/p-try/-/p-try-2.2.0.tgz", "integrity": "sha512-R4nPAVTAU0B9D35/Gk3uJf/7XYbQcyohSKdvAxIRSNghFl4e71hVoGnBNQz9cWaXxO2I10KTC+3jMdvvoKw6dQ==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/parse-json": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/parse-json/-/parse-json-5.2.0.tgz", "integrity": "sha512-ayCKvm/phCGxOkYRSCM82iDwct8/EonSEgCSxWxD7ve6jHggsFl4fZVQBPRNgQoKiuV/odhFrGzQXZwbifC8Rg==", "dev": true, "dependencies": {"@babel/code-frame": "^7.0.0", "error-ex": "^1.3.1", "json-parse-even-better-errors": "^2.3.0", "lines-and-columns": "^1.1.6"}, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/path-exists": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/path-exists/-/path-exists-4.0.0.tgz", "integrity": "sha512-ak9Qy5Q7jYb2Wwcey5Fpvg2KoAc/ZIhLSLOSBmRmygPsGwkVVt0fZa0qrtMz+m6tJTAHfZQ8FnmB4MG4LWy7/w==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-is-absolute": {"version": "1.0.1", "resolved": "https://registry.npmjs.org/path-is-absolute/-/path-is-absolute-1.0.1.tgz", "integrity": "sha512-AVbw3UJ2e9bq64vSaS9Am0fje1Pa8pbGqTTsmXfaIiMpnr5DlDhfJOuLj9Sf95ZPVDAUerDfEk88MPmPe7UCQg==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/path-key": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/path-key/-/path-key-3.1.1.tgz", "integrity": "sha512-ojmeN0qd+y0jszEtoY48r0Peq5dwMEkIlCOu6Q5f41lfkswXuKtYrhgoTpLnyIcHm24Uhqx+5Tqm2InSwLhE6Q==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/path-parse": {"version": "1.0.7", "resolved": "https://registry.npmjs.org/path-parse/-/path-parse-1.0.7.tgz", "integrity": "sha512-LDJzPVEEEPR+y48z93A0Ed0yXb8pAByGWo/k5YYdYgpY2/2EsOsksJrq7lOHxryrVOn1ejG6oAp8ahvOIQD8sw==", "dev": true}, "node_modules/picocolors": {"version": "1.1.1", "resolved": "https://registry.npmjs.org/picocolors/-/picocolors-1.1.1.tgz", "integrity": "sha512-xceH2snhtb5M9liqDsmEw56le376mTZkEX/jEb/RxNFyegNul7eNslCXP9FDj/Lcu0X8KEyMceP2ntpaHrDEVA==", "dev": true}, "node_modules/picomatch": {"version": "2.3.1", "resolved": "https://registry.npmjs.org/picomatch/-/picomatch-2.3.1.tgz", "integrity": "sha512-JU3teHTNjmE2VCGFzuY8EXzCDVwEqB2a8fsIvwaStHhAWJEeVd1o1QD80CU6+ZdEXXSLbSsuLwJjkCBWqRQUVA==", "dev": true, "engines": {"node": ">=8.6"}, "funding": {"url": "https://github.com/sponsors/jonschlinkert"}}, "node_modules/pirates": {"version": "4.0.7", "resolved": "https://registry.npmjs.org/pirates/-/pirates-4.0.7.tgz", "integrity": "sha512-TfySrs/5nm8fQJDcBDuUng3VOUKsd7S+zqvbOTiGXHfxX4wK31ard+hoNuvkicM/2YFzlpDgABOevKSsB4G/FA==", "dev": true, "engines": {"node": ">= 6"}}, "node_modules/pkg-dir": {"version": "4.2.0", "resolved": "https://registry.npmjs.org/pkg-dir/-/pkg-dir-4.2.0.tgz", "integrity": "sha512-HRDzbaKjC+AOWVXxAU/x54COGeIv9eb+6CkDSQoNTt4XyWoIJvuPsXizxu/Fr23EiekbtZwmh1IcIG/l/a10GQ==", "dev": true, "dependencies": {"find-up": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/pretty-format": {"version": "29.7.0", "resolved": "https://registry.npmjs.org/pretty-format/-/pretty-format-29.7.0.tgz", "integrity": "sha512-Pdlw/oPxN+aXdmM9R00JVC9WVFoCLTKJvDVLgmJ+qAffBMxsV85l/Lu7sNx4zSzPyoL2euImuEwHhOXdEgNFZQ==", "dev": true, "dependencies": {"@jest/schemas": "^29.6.3", "ansi-styles": "^5.0.0", "react-is": "^18.0.0"}, "engines": {"node": "^14.15.0 || ^16.10.0 || >=18.0.0"}}, "node_modules/pretty-format/node_modules/ansi-styles": {"version": "5.2.0", "resolved": "https://registry.npmjs.org/ansi-styles/-/ansi-styles-5.2.0.tgz", "integrity": "sha512-Cxwpt2SfTzTtXcfOlzGEee8O+c+MmUgGrNiBcXnuWxuFJHe6a5Hz7qwhwe5OgaSYI0IJvkLqWX1ASG+cJOkEiA==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/ansi-styles?sponsor=1"}}, "node_modules/prompts": {"version": "2.4.2", "resolved": "https://registry.npmjs.org/prompts/-/prompts-2.4.2.tgz", "integrity": "sha512-NxNv/kLguCA7p3jE8oL2aEBsrJWgAakBpgmgK6lpPWV+WuOmY6r2/zbAVnP+T8bQlA0nzHXSJSJW0Hq7ylaD2Q==", "dev": true, "dependencies": {"kleur": "^3.0.3", "sisteransi": "^1.0.5"}, "engines": {"node": ">= 6"}}, "node_modules/pure-rand": {"version": "6.1.0", "resolved": "https://registry.npmjs.org/pure-rand/-/pure-rand-6.1.0.tgz", "integrity": "sha512-b<PERSON><PERSON>awvoZoBYpp6yIoQtQXHZjmz35RSVHnUOTefl8Vcjr8snTPY1wnpSPMWekcFwbxI6gtmT7rSYPFvz71ldiOA==", "dev": true, "funding": [{"type": "individual", "url": "https://github.com/sponsors/dubzzz"}, {"type": "opencollective", "url": "https://opencollective.com/fast-check"}]}, "node_modules/react-is": {"version": "18.3.1", "resolved": "https://registry.npmjs.org/react-is/-/react-is-18.3.1.tgz", "integrity": "sha512-/LLMVyas0ljjAtoYiPqYiL8VWXzUUdThrmU5+n20DZv+a+ClRoevUzw5JxU+Ieh5/c87ytoTBV9G1FiKfNJdmg==", "dev": true}, "node_modules/require-directory": {"version": "2.1.1", "resolved": "https://registry.npmjs.org/require-directory/-/require-directory-2.1.1.tgz", "integrity": "sha512-fGxEI7+wsG9xrvdjsrlmL22OMTTiHRwAMroiEeMgq8gzoLC/PQr7RsRDSTLUg/bZAZtF+TVIkHc6/4RIKrui+Q==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/resolve": {"version": "1.22.10", "resolved": "https://registry.npmjs.org/resolve/-/resolve-1.22.10.tgz", "integrity": "sha512-NPRy+/ncIMeDlTAsuqwKIiferiawhefFJtkNSW0qZJEqMEb+qBt/77B/jGeeek+F0uOeN05CDa6HXbbIgtVX4w==", "dev": true, "dependencies": {"is-core-module": "^2.16.0", "path-parse": "^1.0.7", "supports-preserve-symlinks-flag": "^1.0.0"}, "bin": {"resolve": "bin/resolve"}, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/resolve-cwd": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/resolve-cwd/-/resolve-cwd-3.0.0.tgz", "integrity": "sha512-OrZaX2Mb+rJCpH/6CpSqt9xFVpN++x01XnN2ie9g6P5/3xelLAkXWVADpdz1IHD/KFfEXyE6V0U01OQ3UO2rEg==", "dev": true, "dependencies": {"resolve-from": "^5.0.0"}, "engines": {"node": ">=8"}}, "node_modules/resolve-from": {"version": "5.0.0", "resolved": "https://registry.npmjs.org/resolve-from/-/resolve-from-5.0.0.tgz", "integrity": "sha512-qYg9KP24dD5qka9J47d0aVky0N+b4fTU89LN9iDnjB5waksiC49rvMB0PrUJQGoTmH50XPiqOvAjDfaijGxYZw==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/resolve.exports": {"version": "2.0.3", "resolved": "https://registry.npmjs.org/resolve.exports/-/resolve.exports-2.0.3.tgz", "integrity": "sha512-OcXjMsGdhL4XnbShKpAcSqPMzQoYkYyhbEaeSko47MjRP9NfEQMhZkXL1DoFlt9LWQn4YttrdnV6X2OiyzBi+A==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/semver": {"version": "6.3.1", "resolved": "https://registry.npmjs.org/semver/-/semver-6.3.1.tgz", "integrity": "sha512-BR7VvDCVHO+q2xBEWskxS6DJE1qRnb7DxzUrogb71CWoSficBxYsiAGd+Kl0mmq/MprG9yArRkyrQxTO6XjMzA==", "bin": {"semver": "bin/semver.js"}}, "node_modules/shebang-command": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/shebang-command/-/shebang-command-2.0.0.tgz", "integrity": "sha512-kHxr2zZpYtdmrN1qDjrrX/Z1rR1kG8Dx+gkpK1G4eXmvXswmcE1hTWBWYUzlraYw1/yZp6YuDY77YtvbN0dmDA==", "dev": true, "dependencies": {"shebang-regex": "^3.0.0"}, "engines": {"node": ">=8"}}, "node_modules/shebang-regex": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/shebang-regex/-/shebang-regex-3.0.0.tgz", "integrity": "sha512-7++dFhtcx3353uBaq8DDR4NuxBetBzC7ZQOhmTQInHEd6bSrXdiEyzCvG07Z44UYdLShWUyXt5M/yhz8ekcb1A==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/signal-exit": {"version": "3.0.7", "resolved": "https://registry.npmjs.org/signal-exit/-/signal-exit-3.0.7.tgz", "integrity": "sha512-wnD2ZE+l+SPC/uoS0vXeE9L1+0wuaMqKlfz9AMUo38JsyLSBWSFcHR1Rri62LZc12vLr1gb3jl7iwQhgwpAbGQ==", "dev": true}, "node_modules/sisteransi": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/sisteransi/-/sisteransi-1.0.5.tgz", "integrity": "sha512-bLGGlR1QxBcynn2d5YmDX4MGjlZvy2MRBDRNHLJ8VI6l6+9FUiyTFNJ0IveOSP0bcXgVDPRcfGqA0pjaqUpfVg==", "dev": true}, "node_modules/slash": {"version": "3.0.0", "resolved": "https://registry.npmjs.org/slash/-/slash-3.0.0.tgz", "integrity": "sha512-g9Q1haeby36OSStwb4ntCGGGaKsaVSjQ68fBxoQcutl5fS1vuY18H3wSt3jFyFtrkx+Kz0V1G85A4MyAdDMi2Q==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/source-map": {"version": "0.6.1", "resolved": "https://registry.npmjs.org/source-map/-/source-map-0.6.1.tgz", "integrity": "sha512-UjgapumWlbMhkBgzT7Ykc5YXUT46F0iKu8SGXq0bcwP5dz/h0Plj6enJqjz1Zbq2l5WaqYnrVbwWOWMyF3F47g==", "dev": true, "engines": {"node": ">=0.10.0"}}, "node_modules/source-map-support": {"version": "0.5.13", "resolved": "https://registry.npmjs.org/source-map-support/-/source-map-support-0.5.13.tgz", "integrity": "sha512-SHSKFHadjVA5oR4PPqhtAVdcBWwRYVd6g6cAXnIbRiIwc2EhPrTuKUBdSLvlEKyIP3GCf89fltvcZiP9MMFA1w==", "dev": true, "dependencies": {"buffer-from": "^1.0.0", "source-map": "^0.6.0"}}, "node_modules/sprintf-js": {"version": "1.0.3", "resolved": "https://registry.npmjs.org/sprintf-js/-/sprintf-js-1.0.3.tgz", "integrity": "sha512-D9cPgkvLlV3t3IzL0D0YLvGA9Ahk4PcvVwUbN0dSGr1aP0Nrt4AEnTUbuGvquEC0mA64Gqt1fzirlRs5ibXx8g==", "dev": true}, "node_modules/stack-utils": {"version": "2.0.6", "resolved": "https://registry.npmjs.org/stack-utils/-/stack-utils-2.0.6.tgz", "integrity": "sha512-XlkWvfIm6RmsWtNJx+uqtKLS8eqFbxUg0ZzLXqY0caEy9l7hruX8IpiDnjsLavoBgqCCR71TqWO8MaXYheJ3RQ==", "dev": true, "dependencies": {"escape-string-regexp": "^2.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-length": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/string-length/-/string-length-4.0.2.tgz", "integrity": "sha512-+l6rNN5fYHNhZZy41RXsYptCjA2Igmq4EG7kZAYFQI1E1VTXarr6ZPXBg6eq7Y6eK4FEhY6AJlyuFIb/v/S0VQ==", "dev": true, "dependencies": {"char-regex": "^1.0.2", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}}, "node_modules/string-width": {"version": "4.2.3", "resolved": "https://registry.npmjs.org/string-width/-/string-width-4.2.3.tgz", "integrity": "sha512-wKyQRQpjJ0sIp62ErSZdGsjMJWsap5oRNihHhu6G7JVO/9jIB6UyevL+tXuOqrng8j/cxKTWyWUwvSTriiZz/g==", "dev": true, "dependencies": {"emoji-regex": "^8.0.0", "is-fullwidth-code-point": "^3.0.0", "strip-ansi": "^6.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-ansi": {"version": "6.0.1", "resolved": "https://registry.npmjs.org/strip-ansi/-/strip-ansi-6.0.1.tgz", "integrity": "sha512-Y38VPSHcqkFrCpFnQ9vuSXmquuv5oXOKpGeT6aGrr3o3Gc9AlVa6JBfUSOCnbxGGZF+/0ooI7KrPuUSztUdU5A==", "dev": true, "dependencies": {"ansi-regex": "^5.0.1"}, "engines": {"node": ">=8"}}, "node_modules/strip-bom": {"version": "4.0.0", "resolved": "https://registry.npmjs.org/strip-bom/-/strip-bom-4.0.0.tgz", "integrity": "sha512-3xurFv5tEgii33Zi8Jtp55wEIILR9eh34FAW00PZf+JnSsTmV/ioewSgQl97JHvgjoRGwPShsWm+IdrxB35d0w==", "dev": true, "engines": {"node": ">=8"}}, "node_modules/strip-final-newline": {"version": "2.0.0", "resolved": "https://registry.npmjs.org/strip-final-newline/-/strip-final-newline-2.0.0.tgz", "integrity": "sha512-BrpvfNAE3dcvq7ll3xVumzjKjZQ5tI1sEUIKr3Uoks0XUl45St3FlatVqef9prk4jRDzhW6WZg+3bk93y6pLjA==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/strip-json-comments": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/strip-json-comments/-/strip-json-comments-3.1.1.tgz", "integrity": "sha512-6fPc+R4ihwqP6N/aIv2f1gMH8lOVtWQHoqC4yK6oSDVVocumAsfCqjkXnqiYMhmMwS/mEHLp7Vehlt3ql6lEig==", "dev": true, "engines": {"node": ">=8"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/supports-color": {"version": "7.2.0", "resolved": "https://registry.npmjs.org/supports-color/-/supports-color-7.2.0.tgz", "integrity": "sha512-qpCAvRl9stuOHveKsn7HncJRvv501qIacKzQlO/+Lwxc9+0q2wLyv4Dfvt80/DPn2pqOBsJdDiogXGR9+OvwRw==", "dev": true, "dependencies": {"has-flag": "^4.0.0"}, "engines": {"node": ">=8"}}, "node_modules/supports-preserve-symlinks-flag": {"version": "1.0.0", "resolved": "https://registry.npmjs.org/supports-preserve-symlinks-flag/-/supports-preserve-symlinks-flag-1.0.0.tgz", "integrity": "sha512-ot0WnXS9fgdkgIcePe6RHNk1WA8+muPa6cSjeR3V8K27q9BB1rTE3R1p7Hv0z1ZyAc8s6Vvv8DIyWf681MAt0w==", "dev": true, "engines": {"node": ">= 0.4"}, "funding": {"url": "https://github.com/sponsors/ljharb"}}, "node_modules/test-exclude": {"version": "6.0.0", "resolved": "https://registry.npmjs.org/test-exclude/-/test-exclude-6.0.0.tgz", "integrity": "sha512-cAGWPIyOHU6zlmg88jwm7VRyXnMN7iV68OGAbYDk/Mh/xC/pzVPlQtY6ngoIH/5/tciuhGfvESU8GrHrcxD56w==", "dev": true, "dependencies": {"@istanbuljs/schema": "^0.1.2", "glob": "^7.1.4", "minimatch": "^3.0.4"}, "engines": {"node": ">=8"}}, "node_modules/tmpl": {"version": "1.0.5", "resolved": "https://registry.npmjs.org/tmpl/-/tmpl-1.0.5.tgz", "integrity": "sha512-3f0uOEAQwIqGuWW2MVzYg8fV/QNnc/IpuJNG837rLuczAaLVHslWHZQj4IGiEl5Hs3kkbhwL9Ab7Hrsmuj+Smw==", "dev": true}, "node_modules/to-regex-range": {"version": "5.0.1", "resolved": "https://registry.npmjs.org/to-regex-range/-/to-regex-range-5.0.1.tgz", "integrity": "sha512-65P7iz6X5yEr1cwcgvQxbbIw7Uk3gOy5dIdtZ4rDveLqhrdJP+Li/Hx6tyK0NEb+2GCyneCMJiGqrADCSNk8sQ==", "dev": true, "dependencies": {"is-number": "^7.0.0"}, "engines": {"node": ">=8.0"}}, "node_modules/ts-jest": {"version": "29.4.0", "resolved": "https://registry.npmjs.org/ts-jest/-/ts-jest-29.4.0.tgz", "integrity": "sha512-d423TJMnJGu80/eSgfQ5w/R+0zFJvdtTxwtF9KzFFunOpSeD+79lHJQIiAhluJoyGRbvj9NZJsl9WjCUo0ND7Q==", "dev": true, "dependencies": {"bs-logger": "^0.2.6", "ejs": "^3.1.10", "fast-json-stable-stringify": "^2.1.0", "json5": "^2.2.3", "lodash.memoize": "^4.1.2", "make-error": "^1.3.6", "semver": "^7.7.2", "type-fest": "^4.41.0", "yargs-parser": "^21.1.1"}, "bin": {"ts-jest": "cli.js"}, "engines": {"node": "^14.15.0 || ^16.10.0 || ^18.0.0 || >=20.0.0"}, "peerDependencies": {"@babel/core": ">=7.0.0-beta.0 <8", "@jest/transform": "^29.0.0 || ^30.0.0", "@jest/types": "^29.0.0 || ^30.0.0", "babel-jest": "^29.0.0 || ^30.0.0", "jest": "^29.0.0 || ^30.0.0", "jest-util": "^29.0.0 || ^30.0.0", "typescript": ">=4.3 <6"}, "peerDependenciesMeta": {"@babel/core": {"optional": true}, "@jest/transform": {"optional": true}, "@jest/types": {"optional": true}, "babel-jest": {"optional": true}, "esbuild": {"optional": true}, "jest-util": {"optional": true}}}, "node_modules/ts-jest/node_modules/semver": {"version": "7.7.2", "resolved": "https://registry.npmjs.org/semver/-/semver-7.7.2.tgz", "integrity": "sha512-RF0Fw+rO5AMf9MAyaRXI4AV0Ulj5lMHqVxxdSgiVbixSCXoEmmX/jk0CuJw4+3SqroYO9VoUh+HcuJivvtJemA==", "dev": true, "bin": {"semver": "bin/semver.js"}, "engines": {"node": ">=10"}}, "node_modules/ts-jest/node_modules/type-fest": {"version": "4.41.0", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-4.41.0.tgz", "integrity": "sha512-TeTSQ6H5YHvpqVwBRcnLDCBnDOHWYu7IvGbHT6N8AOymcr9PJGjc1GTtiWZTYg0NCgYwvnYWEkVChQAr9bjfwA==", "dev": true, "engines": {"node": ">=16"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/ts-node": {"version": "10.9.2", "resolved": "https://registry.npmjs.org/ts-node/-/ts-node-10.9.2.tgz", "integrity": "sha512-f0FFpIdcHgn8zcPSbf1dRevwt047YMnaiJM3u2w2RewrB+fob/zePZcrOyQoLMMO7aBIddLcQIEK5dYjkLnGrQ==", "dev": true, "dependencies": {"@cspotcode/source-map-support": "^0.8.0", "@tsconfig/node10": "^1.0.7", "@tsconfig/node12": "^1.0.7", "@tsconfig/node14": "^1.0.0", "@tsconfig/node16": "^1.0.2", "acorn": "^8.4.1", "acorn-walk": "^8.1.1", "arg": "^4.1.0", "create-require": "^1.1.0", "diff": "^4.0.1", "make-error": "^1.1.1", "v8-compile-cache-lib": "^3.0.1", "yn": "3.1.1"}, "bin": {"ts-node": "dist/bin.js", "ts-node-cwd": "dist/bin-cwd.js", "ts-node-esm": "dist/bin-esm.js", "ts-node-script": "dist/bin-script.js", "ts-node-transpile-only": "dist/bin-transpile.js", "ts-script": "dist/bin-script-deprecated.js"}, "peerDependencies": {"@swc/core": ">=1.2.50", "@swc/wasm": ">=1.2.50", "@types/node": "*", "typescript": ">=2.7"}, "peerDependenciesMeta": {"@swc/core": {"optional": true}, "@swc/wasm": {"optional": true}}}, "node_modules/type-detect": {"version": "4.0.8", "resolved": "https://registry.npmjs.org/type-detect/-/type-detect-4.0.8.tgz", "integrity": "sha512-0fr/mIH1dlO+x7TlcMy+bIDqKPsw/70tVyeHW787goQjhmqaZe10uwLujubK9q9Lg6Fiho1KUKDYz0Z7k7g5/g==", "dev": true, "engines": {"node": ">=4"}}, "node_modules/type-fest": {"version": "0.21.3", "resolved": "https://registry.npmjs.org/type-fest/-/type-fest-0.21.3.tgz", "integrity": "sha512-t0rzBq87m3fVcduHDUFhKmyyX+9eo6WQjZvf51Ea/M0Q7+T374Jp1aUiyUl0GKxp8M/OETVHSDvmkyPgvX+X2w==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}, "node_modules/typescript": {"version": "5.6.3", "resolved": "https://registry.npmjs.org/typescript/-/typescript-5.6.3.tgz", "integrity": "sha512-hjcS1mhfuyi4WW8IWtjP7brDrG2cuDZukyrYrSauoXGNgx0S7zceP07adYkJycEr56BOUTNPzbInooiN3fn1qw==", "dev": true, "bin": {"tsc": "bin/tsc", "tsserver": "bin/tsserver"}, "engines": {"node": ">=14.17"}}, "node_modules/undici-types": {"version": "6.19.8", "resolved": "https://registry.npmjs.org/undici-types/-/undici-types-6.19.8.tgz", "integrity": "sha512-ve2KP6f/JnbPBFyobGHuerC9g1FYGn/F8n1LWTwNxCEzd6IfqTwUQcNXgEtmmQ6DlRrC1hrSrBnCZPokRrDHjw==", "dev": true}, "node_modules/update-browserslist-db": {"version": "1.1.3", "resolved": "https://registry.npmjs.org/update-browserslist-db/-/update-browserslist-db-1.1.3.tgz", "integrity": "sha512-UxhIZQ+QInVdunkDAaiazvvT/+fXL5Osr0JZlJulepYu6Jd7qJtDZjlur0emRlT71EN3ScPoE7gvsuIKKNavKw==", "dev": true, "funding": [{"type": "opencollective", "url": "https://opencollective.com/browserslist"}, {"type": "tidelift", "url": "https://tidelift.com/funding/github/npm/browserslist"}, {"type": "github", "url": "https://github.com/sponsors/ai"}], "dependencies": {"escalade": "^3.2.0", "picocolors": "^1.1.1"}, "bin": {"update-browserslist-db": "cli.js"}, "peerDependencies": {"browserslist": ">= 4.21.0"}}, "node_modules/v8-compile-cache-lib": {"version": "3.0.1", "resolved": "https://registry.npmjs.org/v8-compile-cache-lib/-/v8-compile-cache-lib-3.0.1.tgz", "integrity": "sha512-wa7YjyUGfNZngI/vtK0UHAN+lgDCxBPCylVXGp0zu59Fz5aiGtNXaq3DhIov063MorB+VfufLh3JlF2KdTK3xg==", "dev": true}, "node_modules/v8-to-istanbul": {"version": "9.3.0", "resolved": "https://registry.npmjs.org/v8-to-istanbul/-/v8-to-istanbul-9.3.0.tgz", "integrity": "sha512-kiGUalWN+rgBJ/1OHZsBtU4rXZOfj/7rKQxULKlIzwzQSvMJUUNgPwJEEh7gU6xEVxC0ahoOBvN2YI8GH6FNgA==", "dev": true, "dependencies": {"@jridgewell/trace-mapping": "^0.3.12", "@types/istanbul-lib-coverage": "^2.0.1", "convert-source-map": "^2.0.0"}, "engines": {"node": ">=10.12.0"}}, "node_modules/walker": {"version": "1.0.8", "resolved": "https://registry.npmjs.org/walker/-/walker-1.0.8.tgz", "integrity": "sha512-ts/8E8l5b7kY0vlWLewOkDXMmPdLcVV4GmOQLyxuSswIJsweeFZtAsMF7k1Nszz+TYBQrlYRmzOnr398y1JemQ==", "dev": true, "dependencies": {"makeerror": "1.0.12"}}, "node_modules/which": {"version": "2.0.2", "resolved": "https://registry.npmjs.org/which/-/which-2.0.2.tgz", "integrity": "sha512-BLI3Tl1TW3Pvl70l3yq3Y64i+awpwXqsGBYWkkqMtnbXgrMD+yj7rhW0kuEDxzJaYXGjEW5ogapKNMEKNMjibA==", "dev": true, "dependencies": {"isexe": "^2.0.0"}, "bin": {"node-which": "bin/node-which"}, "engines": {"node": ">= 8"}}, "node_modules/wrap-ansi": {"version": "7.0.0", "resolved": "https://registry.npmjs.org/wrap-ansi/-/wrap-ansi-7.0.0.tgz", "integrity": "sha512-YVGIj2kamLSTxw6NsZjoBxfSwsn0ycdesmc4p+Q21c5zPuZ1pl+NfxVdxPtdHvmNVOQ6XSYG4AUtyt/Fi7D16Q==", "dev": true, "dependencies": {"ansi-styles": "^4.0.0", "string-width": "^4.1.0", "strip-ansi": "^6.0.0"}, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/chalk/wrap-ansi?sponsor=1"}}, "node_modules/wrappy": {"version": "1.0.2", "resolved": "https://registry.npmjs.org/wrappy/-/wrappy-1.0.2.tgz", "integrity": "sha512-l4Sp/DRseor9wL6EvV2+TuQn63dMkPjZ/sp9XkghTEbV9KlPS1xUsZ3u7/IQO4wxtcFB4bgpQPRcR3QCvezPcQ==", "dev": true}, "node_modules/write-file-atomic": {"version": "4.0.2", "resolved": "https://registry.npmjs.org/write-file-atomic/-/write-file-atomic-4.0.2.tgz", "integrity": "sha512-7KxauUdBmSdWnmpaGFg+ppNjKF8uNLry8LyzjauQDOVONfFLNKrKvQOxZ/VuTIcS/gge/YNahf5RIIQWTSarlg==", "dev": true, "dependencies": {"imurmurhash": "^0.1.4", "signal-exit": "^3.0.7"}, "engines": {"node": "^12.13.0 || ^14.15.0 || >=16.0.0"}}, "node_modules/y18n": {"version": "5.0.8", "resolved": "https://registry.npmjs.org/y18n/-/y18n-5.0.8.tgz", "integrity": "sha512-0pfFzegeDWJHJIAmTLRP2DwHjdF5s7jo9tuztdQxAhINCdvS+3nGINqPd00AphqJR/0LhANUS6/+7SCb98YOfA==", "dev": true, "engines": {"node": ">=10"}}, "node_modules/yallist": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yallist/-/yallist-3.1.1.tgz", "integrity": "sha512-a4UGQaWPH59mOXUYnAG2ewncQS4i4F43Tv3JoAM+s2VDAmS9NsK8GpDMLrCHPksFT7h3K6TOoUNn2pb7RoXx4g==", "dev": true}, "node_modules/yargs": {"version": "17.7.2", "resolved": "https://registry.npmjs.org/yargs/-/yargs-17.7.2.tgz", "integrity": "sha512-7dSzzRQ++CKnNI/krKnYRV7JKKPUXMEh61soaHKg9mrWEhzFWhFnxPxGl+69cD1Ou63C13NUPCnmIcrvqCuM6w==", "dev": true, "dependencies": {"cliui": "^8.0.1", "escalade": "^3.1.1", "get-caller-file": "^2.0.5", "require-directory": "^2.1.1", "string-width": "^4.2.3", "y18n": "^5.0.5", "yargs-parser": "^21.1.1"}, "engines": {"node": ">=12"}}, "node_modules/yargs-parser": {"version": "21.1.1", "resolved": "https://registry.npmjs.org/yargs-parser/-/yargs-parser-21.1.1.tgz", "integrity": "sha512-tVpsJW7DdjecAiFpbIB1e3qxIQsE6NoPc5/eTdrbbIC4h0LVsWhnoa3g+m2HclBIujHzsxZ4VJVA+GUuc2/LBw==", "dev": true, "engines": {"node": ">=12"}}, "node_modules/yn": {"version": "3.1.1", "resolved": "https://registry.npmjs.org/yn/-/yn-3.1.1.tgz", "integrity": "sha512-Ux4ygGWsu2c7isFWe8Yu1YluJmqVhxqK2cLXNQA5AcC3QfbGNpM7fu0Y8b/z16pXLnFxZYvWhd3fhBY9DLmC6Q==", "dev": true, "engines": {"node": ">=6"}}, "node_modules/yocto-queue": {"version": "0.1.0", "resolved": "https://registry.npmjs.org/yocto-queue/-/yocto-queue-0.1.0.tgz", "integrity": "sha512-rVksvsnNCdJ/ohGc6xgPwyN8eheCxsiLM8mxuE/t/mOVqJewPuO1miLpTHQiRgTKCLexL4MeAFVagts7HmNZ2Q==", "dev": true, "engines": {"node": ">=10"}, "funding": {"url": "https://github.com/sponsors/sindresorhus"}}}}