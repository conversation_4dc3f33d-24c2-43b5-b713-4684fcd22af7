{"name": "user-app-cdk", "version": "0.1.0", "bin": {"user-app-cdk": "bin/user-app-cdk.js"}, "scripts": {"build": "tsc", "watch": "tsc -w", "test": "jest", "cdk": "cdk"}, "devDependencies": {"@types/jest": "^29.5.14", "@types/node": "22.7.9", "aws-cdk": "2.1016.1", "jest": "^29.7.0", "ts-jest": "^29.2.5", "ts-node": "^10.9.2", "typescript": "~5.6.3"}, "dependencies": {"@aws-cdk/aws-dynamodb": "^1.204.0", "@aws-cdk/aws-ec2": "^1.204.0", "@aws-cdk/aws-ecs": "^1.204.0", "@aws-cdk/aws-iam": "^1.204.0", "@aws-cdk/aws-logs": "^1.204.0", "@aws-cdk/aws-servicediscovery": "^1.204.0", "aws-cdk-lib": "2.196.0", "constructs": "^10.0.0"}}