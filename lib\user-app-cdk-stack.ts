import * as cdk from '@aws-cdk/core';
import * as ec2 from '@aws-cdk/aws-ec2';
import * as ecs from '@aws-cdk/aws-ecs';
import * as dynamodb from '@aws-cdk/aws-dynamodb';
import * as servicediscovery from '@aws-cdk/aws-servicediscovery';
import * as ecs_patterns from '@aws-cdk/aws-ecs-patterns';
import * as logs from '@aws-cdk/aws-logs';

export class UserAppCdkStack extends cdk.Stack {
  constructor(scope: cdk.Construct, id: string, props?: cdk.StackProps) {
    super(scope, id, props);

    // Create VPC with public and private subnets
    const vpc = new ec2.Vpc(this, 'UserAppVpc', {
      maxAzs: 2,
      natGateways: 1,
      vpcName: 'user-application-vpc',
      subnetConfiguration: [
        {
          cidrMask: 24,
          name: 'user-app-public-subnet',
          subnetType: ec2.SubnetType.PUBLIC,
        },
        {
          cidrMask: 24,
          name: 'user-app-private-subnet',
          subnetType: ec2.SubnetType.PRIVATE_WITH_NAT,
        }
      ]
    });

    // Create DynamoDB table
    const usersTable = new dynamodb.Table(this, 'UsersTable', {
      tableName: 'user-application-users-table',
      partitionKey: { name: 'id', type: dynamodb.AttributeType.STRING },
      billingMode: dynamodb.BillingMode.PAY_PER_REQUEST,
      removalPolicy: cdk.RemovalPolicy.DESTROY
    });

    // Create ECS Cluster
    const cluster = new ecs.Cluster(this, 'UserAppCluster', {
      vpc,
      clusterName: 'user-application-ecs-cluster'
    });

    // Create Cloud Map namespace for internal service discovery
    const namespace = new servicediscovery.PrivateDnsNamespace(this, 'ServiceNamespace', {
      name: 'user-app.internal',
      vpc,
    });

    namespace.applyRemovalPolicy(cdk.RemovalPolicy.DESTROY);

    // Backend API Task Definition
    const backendTaskDef = new ecs.FargateTaskDefinition(this, 'BackendTaskDef', {
      memoryLimitMiB: 512,
      cpu: 256,
    });

    const backendContainer = backendTaskDef.addContainer('BackendContainer', {
      image: ecs.ContainerImage.fromAsset('./backend'),
      environment: {
        TABLE_NAME: usersTable.tableName,
        AWS_REGION: this.region,
        PORT: '3000'
      },
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'backend',
        logRetention: logs.RetentionDays.ONE_DAY
      })
    });

    backendContainer.addPortMappings({ containerPort: 3000 });

    // Grant DynamoDB access
    usersTable.grantReadWriteData(backendTaskDef.taskRole);

    // Backend service in private subnet with service discovery
    const backendService = new ecs.FargateService(this, 'BackendService', {
      cluster,
      taskDefinition: backendTaskDef,
      desiredCount: 1,
      vpcSubnets: { subnetType: ec2.SubnetType.PRIVATE_WITH_NAT },
      cloudMapOptions: {
        name: 'user-api-backend',
        cloudMapNamespace: namespace,
        dnsRecordType: servicediscovery.DnsRecordType.A
      }
    });

    // Frontend Task Definition
    const frontendTaskDef = new ecs.FargateTaskDefinition(this, 'FrontendTaskDef', {
      memoryLimitMiB: 512,
      cpu: 256,
    });

    const frontendContainer = frontendTaskDef.addContainer('FrontendContainer', {
      image: ecs.ContainerImage.fromAsset('./frontend', {
        buildArgs: {
          REACT_APP_BACKEND_URL: 'http://user-api-backend.user-app.internal:3000'
        }
      }),
      logging: ecs.LogDrivers.awsLogs({
        streamPrefix: 'frontend',
        logRetention: logs.RetentionDays.ONE_DAY
      })
    });

    frontendContainer.addPortMappings({ containerPort: 80 });

    // Frontend service behind an Application Load Balancer (ALB)
    const frontendService = new ecs_patterns.ApplicationLoadBalancedFargateService(this, 'FrontendALBService', {
      cluster,
      taskDefinition: frontendTaskDef,
      publicLoadBalancer: true,
      desiredCount: 1,
      circuitBreaker: { rollback: true }
    });

    frontendService.service.node.addDependency(frontendService.listener);

    // Allow frontend to call backend
    backendService.connections.allowFrom(frontendService.service, ec2.Port.tcp(3000));

    // Outputs
    new cdk.CfnOutput(this, 'FrontendURL', {
      value: `http://${frontendService.loadBalancer.loadBalancerDnsName}`,
      description: 'Stable URL for the frontend application'
    });

    new cdk.CfnOutput(this, 'BackendServiceDiscoveryDNS', {
      value: 'user-api-backend.user-app.internal:3000',
      description: 'Service discovery name for backend'
    });
  }
}
